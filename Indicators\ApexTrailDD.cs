#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

/*  USAGE:
// Access drawdown price levels in strategies:
ApexTrailDD apexDD = ApexTrailDD(ApexMax: 2500, MyMax: 1500, DrawToBar: 20, FontSize12, 
    							FontColor: Brushes.White, Corner: TextPosition.TopRight, 
								StartLine: 1, 
    							MaxDDColor: Brushes.Red, Width: 2, LineSize: 10, 
								DDLimitColor: Brushes.Orange, 2, 10);

double maxDDPrice = apexDD.MaxDrawDownPrice[0];
double limitPrice = apexDD.DrawDownLimitPrice[0];
*/


//This namespace holds Indicators in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Indicators
{
	public class ApexTrailDD : Indicator
	{
		private Account account;
		private double peakBalance;
		private bool hasOpenPositions;
		private double totalUnrealizedPnL;
		private double currentBalance;

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description = @"ApexTrailDD - Displays peak balance and drawdown lines when trades are open";
				Name = "ApexTrailDD";
				Calculate = Calculate.OnBarClose;
				IsOverlay = true;
				DisplayInDataBox = false;
				DrawOnPricePanel = true;
				DrawHorizontalGridLines = false;
				DrawVerticalGridLines = false;
				PaintPriceMarkers = false;
				ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;
				IsSuspendedWhileInactive = true;

				// Input parameters
				MaxDrawDown = 1000;
				DrawDownLimit = 500;
				DrawToBar = 20;
				FontSize = 12;
				FontColor = Brushes.White;
				Corner = TextPosition.TopRight;
				StartingLine = 1;

				// MaxDrawDown line settings
				MaxDDLineColor = Brushes.Red;
				MaxDDLineWidth = 2;
				MaxDDFontSize = 10;

				// DrawDownLimit line settings
				DrawDownLimitLineColor = Brushes.Orange;
				DrawDownLimitLineWidth = 2;
				DrawDownLimitFontSize = 10;

				// Add plots for series exposure
				AddPlot(Brushes.Transparent, "MaxDrawDownPrice");
				AddPlot(Brushes.Transparent, "DrawDownLimitPrice");
			}
			else if (State == State.DataLoaded)
			{
				// Initialize peak balance
				peakBalance = 0;
				hasOpenPositions = false;
				totalUnrealizedPnL = 0;
				currentBalance = 0;

				// Update account info and display immediately when loaded
				UpdateAccountInfo();
				DisplayPeakBalance();

				// Handle drawdown lines based on position status
				if (hasOpenPositions)
				{
					DrawDrawdownLines();
				}
				else
				{
					RemoveDrawObjects();
				}
			}
		}

		protected override void OnBarUpdate()
		{
			// Process in both historical and real-time to show current account status
			if (State != State.Historical && State != State.Realtime)
				return;

			// Get account information (this now also updates peak balance)
			UpdateAccountInfo();

			// Display peak balance text
			DisplayPeakBalance();

			// Handle drawdown lines based on position status
			if (hasOpenPositions)
			{
				DrawDrawdownLines();
			}
			else
			{
				// Clear drawdown lines when no positions
				RemoveDrawObject("MaxDDLine");
				RemoveDrawObject("MaxDDText");
				RemoveDrawObject("DrawDownLimitLine");
				RemoveDrawObject("DrawDownLimitText");

				// Set series values to 0 when no positions
				MaxDrawDownPrice[0] = 0;
				DrawDownLimitPrice[0] = 0;
			}
		}

		private void UpdateAccountInfo()
		{
			// Get the account from chart trader or first available account
			if (account == null)
			{
				ChartControl.Dispatcher.InvokeAsync(() =>
				{
					var accountSelector = Window.GetWindow(ChartControl.Parent)?.FindFirst("ChartTraderControlAccountSelector") as AccountSelector;
					if (accountSelector?.SelectedAccount != null)
					{
						account = accountSelector.SelectedAccount;
					}
					else
					{
						// Fallback to first available account
						lock (Account.All)
						{
							account = Account.All.FirstOrDefault();
						}
					}
				});
			}

			if (account == null)
				return;

			// Calculate current balance and check for open positions
			hasOpenPositions = false;
			totalUnrealizedPnL = 0;

			lock (account.Positions)
			{
				foreach (var position in account.Positions)
				{
					if (position.MarketPosition != MarketPosition.Flat)
					{
						hasOpenPositions = true;
						totalUnrealizedPnL += position.GetUnrealizedProfitLoss(PerformanceUnit.Currency);
					}
				}
			}

			// Get current account balance (realized balance)
			try
			{
				currentBalance = account.Get(AccountItem.CashValue, Currency.UsDollar);
			}
			catch
			{
				// Fallback if cash value is not available
				currentBalance = account.Get(AccountItem.NetLiquidation, Currency.UsDollar) - totalUnrealizedPnL;
			}

			// Calculate total account value (realized + unrealized)
			double totalAccountValue = currentBalance + totalUnrealizedPnL;

			// Update peak balance - this should be the highest total account value ever reached
			// Get the account's baseline peak balance from its performance
			double accountPeakBalance = GetAccountPeakBalance();
			if (accountPeakBalance > peakBalance)
			{
				peakBalance = accountPeakBalance;
			}

			// Also check if current total account value is higher than our tracked peak
			if (totalAccountValue > peakBalance)
			{
				peakBalance = totalAccountValue;
			}
		}

		private double GetAccountPeakBalance()
		{
			if (account == null)
				return 0;

			try
			{
				// Try to get the peak balance from account performance
				// Use cash value as the baseline since StartingCash doesn't exist
				double cashValue = account.Get(AccountItem.CashValue, Currency.UsDollar);
				double realizedPnL = account.Get(AccountItem.RealizedProfitLoss, Currency.UsDollar);

				// The peak should be at least the current cash value
				// In a real implementation, you might want to store this in a file or registry
				// For now, we'll use the current cash value as the minimum peak
				return Math.Max(cashValue, currentBalance);
			}
			catch
			{
				// Fallback to current balance if we can't get performance data
				return currentBalance;
			}
		}

		private void DisplayPeakBalance()
		{
			// Create text with line breaks based on StartingLine parameter
			string peakText = "";
			for (int i = 1; i < StartingLine; i++)
			{
				peakText += "\n"; // Add empty lines for spacing
			}
			peakText += $"Peak Balance: {peakBalance:C}";

			Draw.TextFixed(this, "PeakBalanceText", peakText, Corner,
				FontColor, new SimpleFont("Arial", FontSize), FontColor, Brushes.Transparent, 0);
		}

		private void DrawDrawdownLines()
		{
			if (peakBalance <= 0)
				return;

			// Calculate drawdown prices
			double maxDDPrice = CalculateDrawdownPrice(MaxDrawDown);
			double drawDownLimitPrice = CalculateDrawdownPrice(DrawDownLimit);

			// Update series values
			MaxDrawDownPrice[0] = maxDDPrice;
			DrawDownLimitPrice[0] = drawDownLimitPrice;

			// Draw MaxDrawDown line
			if (maxDDPrice > 0)
			{
				Draw.Line(this, "MaxDDLine", false, DrawToBar, maxDDPrice, 0, maxDDPrice,
					MaxDDLineColor, DashStyleHelper.Solid, MaxDDLineWidth);

				Draw.Text(this, "MaxDDText", false, $"Max DD: {MaxDrawDown:C}", 0, maxDDPrice, 0,
					MaxDDLineColor, new SimpleFont("Arial", MaxDDFontSize), TextAlignment.Left,
					MaxDDLineColor, Brushes.Transparent, 50);
			}

			// Draw DrawDownLimit line
			if (drawDownLimitPrice > 0)
			{
				Draw.Line(this, "DrawDownLimitLine", false, DrawToBar, drawDownLimitPrice, 0, drawDownLimitPrice,
					DrawDownLimitLineColor, DashStyleHelper.Solid, DrawDownLimitLineWidth);

				Draw.Text(this, "DrawDownLimitText", false, $"DD Limit: {DrawDownLimit:C}", 0, drawDownLimitPrice, 0,
					DrawDownLimitLineColor, new SimpleFont("Arial", DrawDownLimitFontSize), TextAlignment.Left,
					DrawDownLimitLineColor, Brushes.Transparent, 50);
			}
		}

		private double CalculateDrawdownPrice(double drawdownAmount)
		{
			if (!hasOpenPositions || peakBalance <= 0 || account == null)
				return 0;

			// Calculate the target balance after drawdown
			double targetBalance = peakBalance - drawdownAmount;

			// Calculate current total account value (realized + unrealized)
			double currentTotalValue = currentBalance + totalUnrealizedPnL;

			// Calculate how much more loss is needed to reach target balance
			double additionalLossNeeded = currentTotalValue - targetBalance;

			if (additionalLossNeeded <= 0)
				return 0; // Already at or below the drawdown level

			// Calculate price movement needed based on current positions for this instrument
			double netQuantity = 0;

			lock (account.Positions)
			{
				foreach (var position in account.Positions)
				{
					if (position.MarketPosition != MarketPosition.Flat && position.Instrument == Instrument)
					{
						netQuantity += position.Quantity; // Keep sign for direction
					}
				}
			}

			if (netQuantity == 0)
				return 0;

			// Calculate price change needed
			double pointValue = Instrument.MasterInstrument.PointValue;
			double priceChangeNeeded = additionalLossNeeded / (Math.Abs(netQuantity) * pointValue);

			// Determine direction - if long position, price needs to go down for loss
			double currentPrice = Close[0];

			if (netQuantity > 0) // Long position
				return currentPrice - priceChangeNeeded;
			else // Short position
				return currentPrice + priceChangeNeeded;
		}

		#region Properties

		[NinjaScriptProperty]
		[Range(1, double.MaxValue)]
		[Display(Name = "Max Draw Down", Description = "Maximum drawdown amount", Order = 1, GroupName = "Parameters")]
		public double MaxDrawDown { get; set; }

		[NinjaScriptProperty]
		[Range(1, double.MaxValue)]
		[Display(Name = "Draw Down Limit", Description = "Drawdown limit amount", Order = 2, GroupName = "Parameters")]
		public double DrawDownLimit { get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Draw To Bar", Description = "Number of bars from right edge to draw lines to", Order = 3, GroupName = "Parameters")]
		public int DrawToBar { get; set; }

		[NinjaScriptProperty]
		[Range(8, 72)]
		[Display(Name = "Font Size", Description = "Font size for peak balance text", Order = 4, GroupName = "Display")]
		public int FontSize { get; set; }

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name = "Font Color", Description = "Color for peak balance text", Order = 5, GroupName = "Display")]
		public Brush FontColor { get; set; }

		[Browsable(false)]
		public string FontColorSerializable
		{
			get { return Serialize.BrushToString(FontColor); }
			set { FontColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[Display(Name = "Corner", Description = "Corner position for peak balance text", Order = 6, GroupName = "Display")]
		public TextPosition Corner { get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Starting Line", Description = "Number of lines from corner to display text", Order = 7, GroupName = "Display")]
		public int StartingLine { get; set; }

		// MaxDrawDown line properties
		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name = "Max DD Line Color", Description = "Color for Max DrawDown line", Order = 8, GroupName = "Max DrawDown Line")]
		public Brush MaxDDLineColor { get; set; }

		[Browsable(false)]
		public string MaxDDLineColorSerializable
		{
			get { return Serialize.BrushToString(MaxDDLineColor); }
			set { MaxDDLineColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Max DD Line Width", Description = "Width for Max DrawDown line", Order = 9, GroupName = "Max DrawDown Line")]
		public int MaxDDLineWidth { get; set; }

		[NinjaScriptProperty]
		[Range(8, 72)]
		[Display(Name = "Max DD Font Size", Description = "Font size for Max DrawDown text", Order = 10, GroupName = "Max DrawDown Line")]
		public int MaxDDFontSize { get; set; }

		// DrawDownLimit line properties
		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name = "DD Limit Line Color", Description = "Color for DrawDown Limit line", Order = 11, GroupName = "DrawDown Limit Line")]
		public Brush DrawDownLimitLineColor { get; set; }

		[Browsable(false)]
		public string DrawDownLimitLineColorSerializable
		{
			get { return Serialize.BrushToString(DrawDownLimitLineColor); }
			set { DrawDownLimitLineColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "DD Limit Line Width", Description = "Width for DrawDown Limit line", Order = 12, GroupName = "DrawDown Limit Line")]
		public int DrawDownLimitLineWidth { get; set; }

		[NinjaScriptProperty]
		[Range(8, 72)]
		[Display(Name = "DD Limit Font Size", Description = "Font size for DrawDown Limit text", Order = 13, GroupName = "DrawDown Limit Line")]
		public int DrawDownLimitFontSize { get; set; }

		// Series properties for external access
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> MaxDrawDownPrice => Values[0];

		[Browsable(false)]
		[XmlIgnore]
		public Series<double> DrawDownLimitPrice => Values[1];

		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private ApexTrailDD[] cacheApexTrailDD;
		public ApexTrailDD ApexTrailDD(double maxDrawDown, double drawDownLimit, int drawToBar, int fontSize, Brush fontColor, TextPosition corner, int startingLine, Brush maxDDLineColor, int maxDDLineWidth, int maxDDFontSize, Brush drawDownLimitLineColor, int drawDownLimitLineWidth, int drawDownLimitFontSize)
		{
			return ApexTrailDD(Input, maxDrawDown, drawDownLimit, drawToBar, fontSize, fontColor, corner, startingLine, maxDDLineColor, maxDDLineWidth, maxDDFontSize, drawDownLimitLineColor, drawDownLimitLineWidth, drawDownLimitFontSize);
		}

		public ApexTrailDD ApexTrailDD(ISeries<double> input, double maxDrawDown, double drawDownLimit, int drawToBar, int fontSize, Brush fontColor, TextPosition corner, int startingLine, Brush maxDDLineColor, int maxDDLineWidth, int maxDDFontSize, Brush drawDownLimitLineColor, int drawDownLimitLineWidth, int drawDownLimitFontSize)
		{
			if (cacheApexTrailDD != null)
				for (int idx = 0; idx < cacheApexTrailDD.Length; idx++)
					if (cacheApexTrailDD[idx] != null && cacheApexTrailDD[idx].MaxDrawDown == maxDrawDown && cacheApexTrailDD[idx].DrawDownLimit == drawDownLimit && cacheApexTrailDD[idx].DrawToBar == drawToBar && cacheApexTrailDD[idx].FontSize == fontSize && cacheApexTrailDD[idx].FontColor == fontColor && cacheApexTrailDD[idx].Corner == corner && cacheApexTrailDD[idx].StartingLine == startingLine && cacheApexTrailDD[idx].MaxDDLineColor == maxDDLineColor && cacheApexTrailDD[idx].MaxDDLineWidth == maxDDLineWidth && cacheApexTrailDD[idx].MaxDDFontSize == maxDDFontSize && cacheApexTrailDD[idx].DrawDownLimitLineColor == drawDownLimitLineColor && cacheApexTrailDD[idx].DrawDownLimitLineWidth == drawDownLimitLineWidth && cacheApexTrailDD[idx].DrawDownLimitFontSize == drawDownLimitFontSize && cacheApexTrailDD[idx].EqualsInput(input))
						return cacheApexTrailDD[idx];
			return CacheIndicator<ApexTrailDD>(new ApexTrailDD(){ MaxDrawDown = maxDrawDown, DrawDownLimit = drawDownLimit, DrawToBar = drawToBar, FontSize = fontSize, FontColor = fontColor, Corner = corner, StartingLine = startingLine, MaxDDLineColor = maxDDLineColor, MaxDDLineWidth = maxDDLineWidth, MaxDDFontSize = maxDDFontSize, DrawDownLimitLineColor = drawDownLimitLineColor, DrawDownLimitLineWidth = drawDownLimitLineWidth, DrawDownLimitFontSize = drawDownLimitFontSize }, input, ref cacheApexTrailDD);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.ApexTrailDD ApexTrailDD(double maxDrawDown, double drawDownLimit, int drawToBar, int fontSize, Brush fontColor, TextPosition corner, int startingLine, Brush maxDDLineColor, int maxDDLineWidth, int maxDDFontSize, Brush drawDownLimitLineColor, int drawDownLimitLineWidth, int drawDownLimitFontSize)
		{
			return indicator.ApexTrailDD(Input, maxDrawDown, drawDownLimit, drawToBar, fontSize, fontColor, corner, startingLine, maxDDLineColor, maxDDLineWidth, maxDDFontSize, drawDownLimitLineColor, drawDownLimitLineWidth, drawDownLimitFontSize);
		}

		public Indicators.ApexTrailDD ApexTrailDD(ISeries<double> input , double maxDrawDown, double drawDownLimit, int drawToBar, int fontSize, Brush fontColor, TextPosition corner, int startingLine, Brush maxDDLineColor, int maxDDLineWidth, int maxDDFontSize, Brush drawDownLimitLineColor, int drawDownLimitLineWidth, int drawDownLimitFontSize)
		{
			return indicator.ApexTrailDD(input, maxDrawDown, drawDownLimit, drawToBar, fontSize, fontColor, corner, startingLine, maxDDLineColor, maxDDLineWidth, maxDDFontSize, drawDownLimitLineColor, drawDownLimitLineWidth, drawDownLimitFontSize);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.ApexTrailDD ApexTrailDD(double maxDrawDown, double drawDownLimit, int drawToBar, int fontSize, Brush fontColor, TextPosition corner, int startingLine, Brush maxDDLineColor, int maxDDLineWidth, int maxDDFontSize, Brush drawDownLimitLineColor, int drawDownLimitLineWidth, int drawDownLimitFontSize)
		{
			return indicator.ApexTrailDD(Input, maxDrawDown, drawDownLimit, drawToBar, fontSize, fontColor, corner, startingLine, maxDDLineColor, maxDDLineWidth, maxDDFontSize, drawDownLimitLineColor, drawDownLimitLineWidth, drawDownLimitFontSize);
		}

		public Indicators.ApexTrailDD ApexTrailDD(ISeries<double> input , double maxDrawDown, double drawDownLimit, int drawToBar, int fontSize, Brush fontColor, TextPosition corner, int startingLine, Brush maxDDLineColor, int maxDDLineWidth, int maxDDFontSize, Brush drawDownLimitLineColor, int drawDownLimitLineWidth, int drawDownLimitFontSize)
		{
			return indicator.ApexTrailDD(input, maxDrawDown, drawDownLimit, drawToBar, fontSize, fontColor, corner, startingLine, maxDDLineColor, maxDDLineWidth, maxDDFontSize, drawDownLimitLineColor, drawDownLimitLineWidth, drawDownLimitFontSize);
		}
	}
}

#endregion
