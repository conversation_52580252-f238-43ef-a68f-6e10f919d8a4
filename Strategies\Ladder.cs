#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;			/// Added for HttpClient	
using System.Net.Http.Headers;	/// Added for AuthenticationHeaderValue
using Newtonsoft.Json;			/// Added for automatic parsing of http response
using Npgsql;					/// Added for Benoit new signal get method Sep2024

using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
//using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.Strategies;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class Ladder : Strategy
	{
		#region GLOBALS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		
		private const string		VERSION = "1.6";
		private const int			MIN_LOOKBACK = 61;
		private const int			ADD_LOOKBACK = 20;
		private const int			MAX_LOOKBACK = 120;
		
        private NpgsqlConnection	postgreSqlConnection = null;
        private List<GreeksData>	signalList = new List<GreeksData>();

		
		private double		dailyProfit = 0;
		private double		beTriggered1Price = 0;
		private double		beTriggered2Price = 0;
		private double		beTriggered3Price = 0;
		private double	 	beTriggered4Price = 0;
		private double		lastSigSwingPriceL = 0;
		private double		lastSigSwingPriceS = 0;
		private double		prvSigSwingPriceL = 0;
		private double		prvSigSwingPriceS = 0;
		private double		lastSetPnL = 0;
		
		private int			contTradesTaken = 0;
		private int			maxContTrades;
		private int			firstEntryBarIdx = -1;
		private int			firstEntryBarsAgo = -1;
		private int			quantityTP3	= 0;
		private int			simpleTicks;
		
		private string		signalDir = "None";
		private string		lastTradeDir = "None";
		private string		manualSignal = "None";
		private string		positionEMAs = "None";
		private string 		lastDashboard = "";
		private string		lastLogMsg = "";
		private string		lastCaller = "";
		private string		lastLogTime = "";
		private string		chartInstrument;
		
		private bool		entriesDisabled = false;
		private bool		trailTriggered = false;
		private bool		dailyLimitHit = false;
		private bool		longOn = true;
		private bool		shortOn = true;
		private bool		firstSignalRecevied = false;
		private bool		contTradesEnabled = false;
		private bool		inContinuationTrade = false;
		private bool		contMaxHit = false;
		private bool		contHadLoss = false;
		private bool		contHad2Loss = false;
		private bool		userRequestedExit = false;
		private bool		inSession = true;
		private bool		noPrevailingToday = false;
		private bool		isNewDay = true;
		private bool		signalLogActivated = false;
		private bool		useTrailingStop;
		private bool		useSimpleTrail;
		private bool		jacobDisabled;
		private bool		jacobDown;
		
		/// These variables were taken from NinjaTrader sample called UnmanagedTemplate
		private Order		entryOrder = null;
		private Order		slOrder = null;
		private Order		tpOrder1 = null;
		private Order		tpOrder2 = null;
		private Order		tpOrder3 = null;
		private string		oco;
		private bool		stopsSubmitted = false;
		
		// These times are in EST, so (right now) we have to add TimeZoneOffset (3) to our PST
		// Need to redo all the times, such that they are all in EST, and TimeZoneOffset should 
		// be -3, not 3; so that would be consistent with all my other EAs.
		private DateTime	lastSignalTime = DateTime.MinValue;
		private DateTime	lastFetchTime = DateTime.MinValue;
		private DateTime	dbFailureTime = DateTime.MinValue;

		private EMA			slowEMA, fastEMA;
		
		private System.Windows.Controls.Button	enableShortButton;
		private System.Windows.Controls.Button	enableLongButton;
		private System.Windows.Controls.Button	exitButton;
		private System.Windows.Controls.Button	logSignalsButton;
		private System.Windows.Controls.Button	manualEnterLong;
		private System.Windows.Controls.Button	manualEnterShort;
		private System.Windows.Controls.Grid	myGrid;
		
		public enum ContTradesModeEnum
		{
			Disabled = 0,
			OnlyBeforeSignal = 1,
			OnlyAfterSignal = 2,
			BeforeAndAfter = 3
		}
		
		public enum ContTradesNumberEnum
		{
			One = 1,
			Two = 2,
			Three = 3,
			Four = 4,
			UntilLoss = 98,
			UntilTwoLoss = 99,
			All = 100
		}
		
		#endregion
		
		protected override void OnStateChange()
		{
			#region State Change Order of Calling
			/// By printing out "State", dicovered this order:
			/*
				OnStateChange(), State = SetDefaults	// 1st clone for listing the strategies available in UI
				OnStateChange(), State = Configure
				OnStateChange(), State = Configure
				OnStateChange(), State = SetDefaults	// 2nd clone for configuring the one selected
				OnStateChange(), State = Terminated
			
				// Then the real one that we care about after we apply the settings:
				OnStateChange(), State = Configure		// Cloned again, so already has defaults set
				OnStateChange(), State = DataLoaded
				OnStateChange(), State = Historical
				OnStateChange(), State = Transition
				OnStateChange(), State = Realtime
				OnStateChange(), State = Terminated		// would be called again when terminated
			*/
			#endregion
			
			#region INPUT DEFAULTS  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
			/// Set Input Defaults
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy to automatically trade Jacob signals";
				Name							= "Ladder";
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 2700;		// 45 mins: Exit @ 13:15 PST
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				Slippage						= 0;
				StartBehavior					= StartBehavior.AdoptAccountPosition;
				TimeInForce						= TimeInForce.Gtc;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				//StopTargetHandling			= StopTargetHandling.ByStrategyPosition;	// method from managed Razor version
				StopTargetHandling				= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade				= 20;
				IsUnmanaged 					= true;
				IsAdoptAccountPositionAware 	= true;		// Not sure if this is correct/needed
				TraceOrders						= false;
				
				/// Disable this property for performance gains in Strategy Analyzer optimizations
				/// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				
				/// Quantity of contract to trade
				Quantity				= 10;
				TradeGamma				= true;
				TradeDelta				= false;
				TradeTheta				= false;
				TradeVega				= false;
				
				/// Maximum delay between time stamp and trade entry
				LastTradeDir			= "None";
				CheckSeconds			= 2;
				MaxDelaySeconds			= 300;		// Seems to be ~1-2 min delay built-in....
				TimeZoneOffset			= 3; 		// Signals are posted EST, so we need to add 3 to get from PST
				
				/// Take trades before first signal, and/or between signals?
				ContTradesMode			= ContTradesModeEnum.Disabled;
				BiDirectional			= true;
				ContTradesNum			= ContTradesNumberEnum.Two;
				ContStrength			= 3;
				ContReqMAPosition		= true;
				SlowEMA_Period			= 120;
				FastEMA_Period			= 20;
				UseContStartTime		= false;
				ContStartTime			= DateTime.Parse("06:40 AM", System.Globalization.CultureInfo.InvariantCulture);
				ContUseEndTime			= false;
				ContEndTime				= DateTime.Parse("10:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				
				/// StopLoss & TakeProfit
				StopLoss				= 100;
				TakeProfit1				= 10;
				QuantityTP1				= 3;
				TakeProfit2				= 30;
				QuantityTP2				= 3;
				TakeProfit3				= 101;
				
				/// Daily Targets
				DailyMinTarget			= 0;		
				DailyMaxLoss			= 0;
				
				/// Move to BreakEven
				BETrigger1				= 72;
				BEOffset1				= -2;
				BETrigger2				= 0;
				BEOffset2				= 0;
				BETrigger3				= 0;
				BEOffset3				= 0;
				BETrigger4				= 0;
				BEOffset4				= 0;
				
				/// Trailing
				UseTrailingStop 		= false;
				TS_UseSimpleTrail		= false;
				TS_SimpleTicks			= 32;
				TS_UseClose				= true;
				TS_TakeProfitTicks 		= 300;
				TS_OffsetTicks 			= -2;		// Negative for extra room, positive to lock in more
				TS_ActivationTicks 		= 31;
				TS_MinTicks				= 31;
				TS_MaxTicks				= 39;
				TS_ReAdjustAtTicks1 	= 50;
				TS_MinTicks1			= 23;
				TS_MaxTicks1			= 29;
				TS_ReAdjustAtTicks2 	= 80;
				TS_MinTicks2			= 16;
				TS_MaxTicks2			= 20;
				UseEoDTrail				= false;
				TS_EoDStartTime			= DateTime.Parse("12:48 PM", System.Globalization.CultureInfo.InvariantCulture);
				TS_EoDTicks				= 10;
				
				/// Min Volume to trade
				AveVolumePeriod 		= 14;
				MinAveVolume			= 50;
				
				LimitTradingHours		= true;
				StartTime				= DateTime.Parse("06:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime 				= DateTime.Parse("12:50 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime			= true;
				CloseTime 				= DateTime.Parse("12:59 PM", System.Globalization.CultureInfo.InvariantCulture);
				
				StrategyName			= "Ladder";
				DisplayOCD				= true;
				PlotHist				= false;
				DisableLogging			= false;
				UseOutput2				= false;
				UniqueID				= "1";
				UseMarket				= false;
			}
			#endregion
			
			/// Initialize Member Variables
			else if (State == State.Configure)
			{
				maxContTrades = (int) ContTradesNum;
				chartInstrument = this.Instrument.FullName;
				quantityTP3 = Quantity - (QuantityTP1 + QuantityTP2);
				useTrailingStop = UseTrailingStop;
				useSimpleTrail = TS_UseSimpleTrail;
				simpleTicks = TS_SimpleTicks;
				jacobDisabled = (!(TradeGamma  ||  TradeDelta  ||  TradeTheta  ||  TradeVega));

				AddDataSeries(BarsPeriodType.Tick, 1);
			}
			
			else if (State == State.DataLoaded)
			{
				//UpdatePropertyVisibility();
				slowEMA = EMA(SlowEMA_Period);
				fastEMA = EMA(FastEMA_Period);
			}
			
			/// Historical is called once the object begins to process historical data. This state is called once 
			/// when running an object in real-time. This object is called multiple times when running a backtest 
			/// optimization and the property IsInstantiatedOnEachOptimizationIteration is true (default behavior)
			else if (State == State.Historical)
			{
				/// Init user override of lasttradeDir
				/// Not sure this is the best place for this, but seems to work
				if (LastTradeDir.ToUpper() == "LONG")		lastTradeDir = "Long";
				else
				if (LastTradeDir.ToUpper() == "SHORT")		lastTradeDir = "Short";
				
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}	
			}
			
			/// Transition is called once as the object has finished processing historical data but before it starts to process realtime data.
			else if (State == State.Transition)
			{
				dailyProfit = 0;
				dailyLimitHit = false;
				Log("                           --  State == Transition: Reset Daily Profit to ZERO  --");
			}
			
			/// Realtime is called once when the object begins to process realtime data.
			else if (State == State.Realtime)
			{
			}
			
			else if (State == State.Terminated)
			{
				if (postgreSqlConnection != null)
				{
					Log($"CLOSING DB...");
					postgreSqlConnection.Close();
					postgreSqlConnection.Dispose();
					postgreSqlConnection = null;
				}
				
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
			}
		}

		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			Log($"Name = {execution.Name}, IsEntryStrategy = {execution.IsEntryStrategy},  OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   quantity (parameter) = {quantity},   Position.Quantity = {Position.Quantity}");
			
			// We advise monitoring OnExecution to trigger submission of stop/target orders instead of OnOrderUpdate()
			// since OnExecution() is called after OnOrderUpdate() which ensures your strategy has received the execution
			// which is used for internal signal tracking.
			
			double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
			double avePrice = AvePrice();

			//Log("TOP OF FUNC");
			if (entryOrder != null  &&  entryOrder == execution.Order)
			{
				//Log("entryOrder != null  &&  entryOrder == execution.Order");
				string type = (execution.IsEntryStrategy) ? "        ENTRY" : "        EXIT";
				
				/// If part filled, I do nothing, and hope that this method gets called again with order state == filled. 
				/// If that never happens, not sure what is the "right" thing to do, or how to tell it never happened...
				if (execution.Order.OrderState == OrderState.PartFilled)
					Log($"{type} ORDER ONLY PARTIALLY FILLED; execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}");

				else if (execution.Order.OrderState == OrderState.Cancelled)
				{
					/// If the order was somehow cancelled, then we need to just and position that partially opened
					if (execution.Order.Filled > 0)
					{
						Log($"{type} ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}, so closing out those parials");
						ClosePosition("Partial Fill", marketPosition.ToString(), execution.Order.Filled);
					}
					else
						Log($"{type} ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}; Doing Nothing");
				}
				else if (execution.Order.OrderState == OrderState.Rejected)
				{
					Log($"{type} ORDER REJECTED; execution.Order.Filled = {execution.Order.Filled}; Doing Nothing");
				}
				else if (execution.Order.OrderState == OrderState.Filled)
					Log($"{type} ORDER FILLED (execution.Order.OrderState == OrderState.Filled); execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}");
			}
			else
				Log($"entryOrder = {entryOrder}, execution.Order.OrderState = {execution.Order.OrderState}");

			/// IS ENTRY STRATEGY - Handle actions for execution.Order.OrderAction.Buy or SellShort
			if (execution.IsEntryStrategy)
			{
				if (StopLoss > 0  ||  TakeProfit1 > 0  ||  TakeProfit2 > 0  ||  TakeProfit3 > 0)
				{
					/// [Only] if we are using both stops do we need to link OCO
					oco = "";
					if (StopLoss > 0  &&  TakeProfit3 > 0)
					{
						if (!stopsSubmitted)
						{
							/// TakeProfit is required (if using any TP), TakeProfit 1 & 2 are incidental
							if (State == State.Historical)
								oco = Times[0][0].ToString() + "_" + CurrentBar + "_" + "Exits";
							else
								oco = GetAtmStrategyUniqueId() + "_" + "Exits";
							//Log($"Set OCO = {oco}");
						}
						else if (slOrder != null)
						{
							oco = slOrder.Oco;
							//Log($"Fetched existing OCO = {oco}");
						}
						else
							Log($"Could not fetch OCO because slOrder == null");
					}
					
					if (entryOrder != null  &&  entryOrder == execution.Order)
					{
						if (execution.Order.OrderState == OrderState.Filled)
						{
							if (!stopsSubmitted)
							{
								/// This used to check that slOrder & tpOrders were null, because we were initially setting, 
								/// but it won't place the stops until order is filled, so no need to check for that. 
								var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								Log($"INIT PLACEMENT OF SL & TP; Action = {action.ToString()}");
								if (StopLoss > 0)
								{
									Log($"Submitting StopLoss for Qty {Position.Quantity}");
									SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, avePrice - m * StopLoss * TickSize, oco, "StopLoss");
								}
								if (TakeProfit1 > 0)
								{
									Log($"Submitting TakeProfit1 for Qty {QuantityTP1}");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, QuantityTP1, avePrice + m * TakeProfit1 * TickSize, 0, "", "TakeProfit1");
								}
								if (TakeProfit2 > 0)
								{
									Log($"Submitting TakeProfit2 for Qty {QuantityTP2}");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, QuantityTP2, avePrice + m * TakeProfit2 * TickSize, 0, "", "TakeProfit2");
								}
								if (TakeProfit3 > 0)
								{
									Log($"Submitting TakeProfit3 for Qty {quantityTP3}");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, quantityTP3, avePrice + m * TakeProfit3 * TickSize, 0, oco, "TakeProfit3");
								}
								stopsSubmitted = true;
							}
							else
								Log($"execution.Order.OrderState == OrderState.Filled & stops already submitted; DOING NOTHING");
						}
					}
					else
						Log($"entryOrder = {entryOrder}, execution.Order.OrderState = {execution.Order.OrderState}");
				}
			}
			
			/// IS EXIT STRATEGY - Handle actions for execution.Order.OrderAction.Sell or BuyToCover
			else
			{
				/// If we did NOT just close *entire* position, update SL quantity
				if (Position.Quantity > 0)
				{
					if (execution.Order.OrderState == OrderState.Filled)
					{
						Log($"Position.Quantity > 0; Changing SL order to Qty ({Position.Quantity}) @ price {avePrice - m * StopLoss * TickSize}");
						ModifyStopLoss(avePrice - m * StopLoss * TickSize);
					}
				}
				else
				{
					/// Cancel any stops that are left open. SL & TP3 OCO, but check all to be sure
					if (CancelStops())
						Log($"Position.Quantity == 0; cancelled stops");
					
					/// Reset some variables
					Log($"Position.Quantity == 0; RESETTING VARS & checking DailyMaxLoss & DailyMinTarget..");
					stopsSubmitted = false;
					beTriggered1Price = beTriggered2Price = beTriggered3Price = beTriggered4Price = 0;
					trailTriggered = false;
					inContinuationTrade = false;
					firstEntryBarIdx = -1;
					entryOrder = slOrder = tpOrder1 = tpOrder2 = tpOrder3 = null;
					
			        /// Use SystemPerformance.AllTrades to get the last closed trade
			        if (SystemPerformance.AllTrades.Count > 0)
			        {
			            var lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
			
			            /// Access trade details
						double pnl = lastTrade.ProfitCurrency;	// includes commission
						Log($"Last trade PnL: ${pnl}");
						//double com = lastTrade.Commission;
						//Log($"Last trade Commission: ${com}");
						
						dailyProfit += pnl;		// we need to save this each time, so we can reload it if we have to restart platform
						
						/// Don't know how to get the last item from the list.  Should be "Item[]" or "Item()", but does not work:
						//lastSetPnL = tradeSetsPnL[tradeSetsPnL.Count-1];
						//tradeSetsPnL.Add(pnl);
						lastSetPnL += pnl;		
			            Log($"Current set PnL: ${lastSetPnL}");
			        }
					
					/// Check for daily profit/loss limits
					Log($"DailyMinTarget = {DailyMinTarget}, DailyMaxLoss = ${DailyMaxLoss}");
					Log($"pDaily = (${dailyProfit}), SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit = (${SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit})");
					if (DailyMaxLoss > 0  &&  -dailyProfit > DailyMaxLoss)
					{
						/// if (Position.Quantity == 0), Exiting should be unnecessary, but are 
						/// left in case we are reversing position, or otherwise 'just to be safe'
						ClosePosition("Daily Loss");
						Log($"pDaily (${dailyProfit}) - NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) > DailyMaxLoss (${DailyMaxLoss}");
						Log($"Daily Loss Hit! Set dailyLimitHit = true\n");
						dailyLimitHit = true;
					}
					if (DailyMinTarget > 0  &&  dailyProfit > DailyMinTarget)
					{
						ClosePosition("Daily Target");
						Log($"NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) - dailyProfit (${dailyProfit}) > DailyMinTarget (${DailyMinTarget}");
						Log($"Daily Profit Hit! Set dailyLimitHit = true\n");
						dailyLimitHit = true;
					}
				}
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}

		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity, 
											  int filled, double averageFillPrice, Cbi.OrderState orderState, 
											  DateTime time, Cbi.ErrorCode error, string comment)
        {
			//string cmnt = (comment == "") ? "" : ", comment is {comment}";
			//Log($"order.Name is {order.Name}, orderState is {orderState}{cmnt}");
			
			string name = order.Name;
			string action = order.OrderAction.ToString();
			double stopPrc = order.StopPrice;			
			double limitPrc = order.LimitPrice;	
			
			/// One time only, as we transition from historical to live, convert any old historical 
			/// order object references to the live order submitted to the real-time account
			/// This addresses the error: "Strategy XXX has been disabled because it attempted to 
			/// modify a historical order that has transitioned to a live order"
			if (entryOrder != null  &&  entryOrder.IsBacktestOrder  &&  State == State.Realtime)
				entryOrder = GetRealtimeOrder(entryOrder);
			
			/* Removed in favor of conditional below
			if (order.OrderState == OrderState.CancelSubmitted  
			||  order.OrderState == OrderState.CancelPending  
			||  order.OrderState == OrderState.Cancelled  
			||  order.OrderState == OrderState.Rejected  
			||  order.OrderState == OrderState.Unknown)
			{
				Log($"order.Name = {name} : SKIPPING setting Order variable because OrderState is {order.OrderState}, OrderAction = {action}, OrderType = {order.OrderType}, limitPrice = {limitPrice}, stopPrice = {stopPrice}");
				lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
				return;
			}
			*/
			
			if (order.OrderState == OrderState.Accepted  
			||  order.OrderState == OrderState.Working  
			||  order.OrderState == OrderState.Filled)
			{
				/// Assign Order objects here - This is called first when placing/cencelling/closing/modifying 
				/// an order.  OnExecUpdate is called after it's all done -this  may get called several times 
				/// first; e.g. for cancelling a SL, it will get called for OrderState.CancelSubmitted, then 
				/// OrderState.CancelPending, and finally OrderState.Cancelled.  THEN OnExecUpdate is called...
				if (order.Name == "Entry"  ||  order.Name == "Cont.")
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting entryOrder var, OrderAction = {action}");
					entryOrder = order;
				}
				else if (order.Name == "StopLoss")
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting slOrder var, price = {stopPrc}");
					slOrder = order;
				}
				else if (order.Name == "TakeProfit1")
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting tpOrder1 var, price = {limitPrc}");
					tpOrder1 = order;
				}
				else if (order.Name == "TakeProfit2")
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting tpOrder2 var, price = {limitPrc}");
					tpOrder2 = order;
				}
				else if (order.Name == "TakeProfit3")
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting tpOrder3 var, price = {limitPrc}");
					tpOrder3 = order;
				}
				lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
			}
		}
		
		
		protected override void OnBarUpdate()
		{
			/// Only update on-chart display in real-time
			if (State != State.Historical)
			{
				if (CurrentBars[0] < BarsRequiredToTrade  ||  CurrentBars[1] < BarsRequiredToTrade)
				{
					Log($"Number of bars (BiP[0]: {CurrentBars[0]} or BiP[1]: {CurrentBars[1]}) is less than BarsRequiredToTrade ({BarsRequiredToTrade}); Returning");
					lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
					return;
				}
				ManageOCD();	// Update the On-Chart Display
			}
			
			/// Initialize on first bar of historical run
			else if (!PlotHist) 
				return; 
				
			if (lastSignalTime == DateTime.MinValue)
			{
				/// Need to init as near current time so we do not end up trading a signal 
				/// on first tick which is actually not recent (see ParseSignals for time check)
				lastSignalTime = Time[0].AddHours(TimeZoneOffset).AddSeconds(-(MaxDelaySeconds+1));
				Log($"Initialize lastSignalTime to {lastSignalTime.ToString()}, state = {State.ToString()}");
			}
			
			/// Entered on close of each candle of chart TF where strategy is applied (first data series)
			bool highFractal, lowFractal;
			if (BarsInProgress == 0)
			{
				if (Bars.IsFirstBarOfSession)
				{
					/// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
					/// And more importantly, we do not do anthing with historical (yet); that is where this is important:
					/// to reset some daily things that will be done with running over historical bars.
					isNewDay = true;
					dailyProfit = 0;	//SystemPerformance.AllTrades.TradesPerformance.NetProfit;
					dailyLimitHit = false;
					noPrevailingToday = false;
					firstSignalRecevied = false;
					lastTradeDir = "None";
					contTradesTaken = 0;
					contMaxHit = contHadLoss = contHad2Loss = false;
					lastSignalTime = Time[0].AddHours(TimeZoneOffset).AddSeconds(-(MaxDelaySeconds+1));
					
					useSimpleTrail = TS_UseSimpleTrail;
					simpleTicks = TS_SimpleTicks;
					useTrailingStop = UseTrailingStop;
					trailTriggered = false;					
					//tradeSetsPnL.Clear();
					Log($"\nNEW SESSION (DAY) - RESET EVERYTHING - Time[0] = {Time[0].ToString()}\n");
					
					/// This code was in a sample showing how to get all previous profit made 
					/// by this strategy in the past, but SINCE IT WAS APPLIED TO THE CHART
					//int priorTradesCount = SystemPerformance.AllTrades.Count;
					//double priorTradesCumProfit = SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
					/// This is not useful for us because we do not have historical signals [yet] (see comment at top)
				}
				
				/// Close all trades at end of user-defined session
				if (UseCloseTime  &&  Time[0].TimeOfDay >= CloseTime.TimeOfDay)//  &&  Time[1].TimeOfDay < CloseTime.TimeOfDay)
				{
					if (Position.MarketPosition != MarketPosition.Flat)
					{
						// This does not seem to be working... Add logs
						Log($"Time[0] ({Time[0].TimeOfDay.ToString()}) >= CloseTime.TimeOfDay ({CloseTime.TimeOfDay.ToString()})");//  &&  Time[1] ({Time[1].TimeOfDay.ToString()}) < CloseTime.TimeOfDay");
						Log($"End of Session Close activated @ {Time[0].ToString()}; Closing all open orders");
						ClosePosition("EoD Close", Position.MarketPosition.ToString());
					}
				}

				
				/// Set up how many bars ago was the entry candle
				firstEntryBarsAgo = (firstEntryBarIdx != -1) ? CurrentBar - firstEntryBarIdx : -1;
				
				/// Need position of EMAs relative to one another for cont entry option
				if (fastEMA[0] == slowEMA[0])		positionEMAs = "None";
				else if (fastEMA[0] > slowEMA[0])	positionEMAs = "Long";
				else if (fastEMA[0] < slowEMA[0])	positionEMAs = "Short";

				/// If not in market and not in-session (or limit hit), no need to go further
				if (!inSession  ||  dailyLimitHit)
					if (Position.MarketPosition == MarketPosition.Flat)
						return;

				#region CONTINUATION ENTRY  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
				/// NEED TO CHANGE THIS, SO THAT IF IT RECEIVES A REAL (opposite) SIGNAL WHILE IN A CONT TRADE, CLOSE IT AND REVERSE
				if (!entriesDisabled  &&  Position.MarketPosition == MarketPosition.Flat  &&  contTradesEnabled)
				{
					//Log($"CONT PRESECTION, UseContStartTime = {UseContStartTime}, Time[0].TimeOfDay = {Time[0].TimeOfDay.ToString()}, ContStartTime.TimeOfDay = {ContStartTime.TimeOfDay}");
					string direction = "None";
					string ltd = lastTradeDir;
					
					/// If no lastTradeDir, then we want to check both directions (if BiDirectional enabled)
					if (ltd == "None"  &&  BiDirectional)
					{
						/// If candle is closing above, it'd have to be a long candle...
						if (Close[0] > Open[0])	ltd = "Long";
						else					ltd = "Short";
						//Log($"lastTradeDir == None & BiDirectional = {BiDirectional}; so using lastTradeDir as {ltd}");
					}
					/// Check every candle because any cond above could be false, then there would be a gap...
					if (ltd != "None")
					{
						// Get both high & low each time just to update OCD
						prvSigSwingPriceL = lastSigSwingPriceL;
						prvSigSwingPriceS = lastSigSwingPriceS;
						lastSigSwingPriceL = GetLastSignificantSwingPrice("Long");
						lastSigSwingPriceS = GetLastSignificantSwingPrice("Short");
						if (prvSigSwingPriceL != lastSigSwingPriceL  ||  prvSigSwingPriceS != lastSigSwingPriceS)
							Log($"SWING PRICES: lastSigSwingPriceL = {lastSigSwingPriceL},  lastSigSwingPriceS = {lastSigSwingPriceS},  last candle dir = {ltd}");
						
						if (ltd == "Long"  &&  lastSigSwingPriceL > 0  &&  Close[0] > lastSigSwingPriceL)
						{
							direction = "Long";
							Log($"No trades open, TradeContinuation enabled, lastTradeDir = {ltd}, and candle closed ({Close[0]}) above previous fractal @ {lastSigSwingPriceL}; Continuation signal = {direction}");
						}
						else if (ltd == "Short"  &&  lastSigSwingPriceS > 0  &&  Close[0] < lastSigSwingPriceS)
						{
							direction = "Short";
							Log($"No trades open, TradeContinuation enabled, lastTradeDir = {ltd}, and candle closed ({Close[0]}) below previous fractal @ {lastSigSwingPriceS}; Continuation signal = {direction}");
						}
						
						// Confirm EMAf/EMAs position
						if (direction != "None"  &&  ContReqMAPosition  &&  positionEMAs != direction)
						{
							Log($"{direction} Continuation trade cancelled because EMA positions ({positionEMAs}) do not match");
							direction = "None";
						}
					}
					else Log($"LTD == none");
					
					if (direction != "None")
					{
						Log($"\nContinuation Signal Detected @ {Time[0].ToString()}!  Direction = {direction}; checking trade filters...");
						if (TradeFiltersOkay(direction))
						{
							Log($"Trade Filters Okay; ENTERING {direction.ToUpper()} CONTINUATION TRADE");
							PlaceMarketOrder(direction, Quantity, "Cont.");
							contTradesTaken++;
							inContinuationTrade = true;
							lastTradeDir = ltd;
						}
					}
					/// No need to do any trail computations if we were flat a moment ago, or still are
					lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
					return;
				}
				#endregion
			}

			/// Entered on each tick (2nd data series)
			else if (BarsInProgress == 1)
			{
				/// Check each tick whether we are in-session (when it can place new trades)
				inSession = CheckSession();
				
				if (signalLogActivated)
				{
					FetchGreeksData(true, true);
					signalLogActivated = false;
				}
				
				/// If not in market and not in-session (or limit hit), no need to go further
				if ((!inSession  ||  dailyLimitHit)  &&  manualSignal == "None")
					if (Position.MarketPosition == MarketPosition.Flat)
						return;
				
				#region BREAKEVEN - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
				/// No need to check anything further if no position open
				if (Position.MarketPosition != MarketPosition.Flat)
				{
					double newStopPrice = 0;
					double oldPrice = 0;
	
					/// Get existing stop price if exists
					/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
					if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
					{
						oldPrice = slOrder.StopPrice;
						//Log($"Old Stop Price = {oldPrice});
					}
	
					/// Had this issue above in OnExecutionUpdate(); so same here...?
					/// Turns out the marketPosition passed in may not be the same as our actual position direction,
					/// perhaps because this is called in reference to the stop postion(??).  So get correct one
					//var currentMarketPosition = Position.MarketPosition;
					
					double avePrice = AvePrice();
					if (Position.MarketPosition == MarketPosition.Long)
					{
						/// Calculate new stop price
						if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  High[0] >= avePrice + BETrigger1*TickSize)
							beTriggered1Price = newStopPrice = avePrice + BEOffset1*TickSize;
						else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  High[0] >= avePrice + BETrigger2*TickSize)
							beTriggered2Price = newStopPrice = avePrice + BEOffset2*TickSize;
						else if (beTriggered3Price == 0  &&  BETrigger3 != 0  &&  High[0] >= avePrice + BETrigger3*TickSize)
							beTriggered3Price = newStopPrice = avePrice + BEOffset3*TickSize;
						else if (beTriggered4Price == 0  &&  BETrigger4 != 0  &&  High[0] >= avePrice + BETrigger4*TickSize)
							beTriggered4Price = newStopPrice = avePrice + BEOffset4*TickSize;
	
						if (newStopPrice != 0)
							Log($"MoveToBE TRIGGERED: newStopPrice set to {newStopPrice}.  Old stop price = {oldPrice}");
						
						/// Abort if new price does not tighten stop
						if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice <= oldPrice)
							newStopPrice = 0;
					}
					else if (Position.MarketPosition == MarketPosition.Short)
					{
						/// Calculate new stop price
						if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  Low[0] <= avePrice - BETrigger1*TickSize)
							beTriggered1Price = newStopPrice = avePrice - BEOffset1*TickSize;
						else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  Low[0] <= avePrice - BETrigger2*TickSize)
							beTriggered2Price = newStopPrice = avePrice - BEOffset2*TickSize;
						else if (beTriggered3Price == 0  &&  BETrigger3 != 0  &&  Low[0] <= avePrice - BETrigger3*TickSize)
							beTriggered3Price = newStopPrice = avePrice - BEOffset3*TickSize;
						else if (beTriggered4Price == 0  &&  BETrigger4 != 0  &&  Low[0] <= avePrice - BETrigger4*TickSize)
							beTriggered4Price = newStopPrice = avePrice - BEOffset4*TickSize;
						
						if (newStopPrice != 0)
							Log($"MoveToBE TRIGGERED: newStopPrice set to {newStopPrice}.  Old stop price = {oldPrice}");
						
						/// Abort if new price does not tighten stop
						if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice >= oldPrice)
							newStopPrice = 0;
					}
					if (newStopPrice != 0)
					{
						Log($"Calling ModifyStopLoss()");
						ModifyStopLoss(newStopPrice);
					}
				}
				#endregion
				
				#region ENABLE CONTINUATION TRADES? - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
				/* I don't think I can use new switch syntax because not simple assignemnt
				string contTradesEnabled = ContTradesMode switch
				{
					ContTradesModeEnum.OnlyAfterSignal => true,
					ContTradesModeEnum.OnlyBeforeSignal => false,
					ContTradesNumberEnum.UntilLoss => (lastSetPnL > 0) ? true : false,
					_ => false
				};		//....and this...is not simple:  */
				
				/// Assign whether we are active for continuation trades
				if (!contMaxHit  &&  contTradesTaken >= maxContTrades)
					contMaxHit = true;
				if (lastSetPnL < 0)
				{
					if (!contHadLoss)
					{
						if (maxContTrades == (int) ContTradesNumberEnum.UntilLoss  ||  maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
							contHadLoss = true;
					}
					/// May want to make it two IN A ROW, which would require more coding...
					else if (maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
						contHad2Loss = true;
				}
				
				if (contMaxHit)
					contTradesEnabled = false;
				else if (contHadLoss  &&  maxContTrades == (int) ContTradesNumberEnum.UntilLoss)
					contTradesEnabled = false;
				else if (contHad2Loss  &&  maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
					contTradesEnabled = false;
				else if (ContUseEndTime  &&  Time[0].TimeOfDay >= ContEndTime.TimeOfDay)
					contTradesEnabled = false;
				else if (UseContStartTime  &&  Time[0].TimeOfDay < ContStartTime.TimeOfDay)
					contTradesEnabled = false;				
				else
				{
					switch (ContTradesMode)
					{
						case ContTradesModeEnum.OnlyBeforeSignal:
							contTradesEnabled = !firstSignalRecevied;
							break;
						case ContTradesModeEnum.OnlyAfterSignal:
							contTradesEnabled = firstSignalRecevied;
							break;
						case ContTradesModeEnum.BeforeAndAfter:
							contTradesEnabled = true;
							break;
						default:
						case ContTradesModeEnum.Disabled:
							contTradesEnabled = false;
							break;
					}
				}
				#endregion
				
				#region MAIN SIGNAL ENTRY - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
				signalDir = "None";
				if (!entriesDisabled)
				{
					DateTime timeNow = DateTime.Now;
					if (State == State.Historical)
						timeNow = Time[0];
				
					/// Force an entry?
					if (manualSignal == "Long"  ||  manualSignal == "Short")
						signalDir = manualSignal;
					
					/// Check for valid signal from DB
					else if (!jacobDisabled  &&  timeNow >= lastFetchTime.AddSeconds(CheckSeconds))
					{
						//Log($"               Fetching Data @ {timeNow.ToString()} local time");
						signalList = FetchGreeksData();
						lastFetchTime = timeNow;
						if (signalList.Count > 0)
							ParseSignals();
					}

					if (signalDir != "None")
					{
						string extra = (manualSignal == "None") ? "Jacob " : "MANUAL ";
						Log($"{signalDir} {extra}Signal Received @ {timeNow.ToString()}; checking trade filters...");
						if (TradeFiltersOkay(signalDir))
						{
							Log($"\nTrade Filters Okay; ENTERING {signalDir.ToUpper()} TRADE");
							
							/// If this is a user-initiated signal...
							if (manualSignal == signalDir)
							{
								/// Changed all manual entry to use market orders, so just set
								/// lastSignalTime = time of current chart bar (for the reference in ParseSignals)
								lastSignalTime = Times[0][0];
								Log($"Manual Entry: lastSignalTime set to {lastSignalTime}");

								/// If !flat, then open position must be opposite, cause we already checked for same direction 
								/// in button click method. Log, but no need to close trade here; PlaceMarketOrder will close it.
								if (Position.MarketPosition != MarketPosition.Flat)
									Log($"Manual Entry: Requested dir opposite of open position; existing position will be closed");
							}
							
							/// Enter all user-triggered entries at market
							if (UseMarket  ||  manualSignal == signalDir)
							{
								Log($"Placing {signalDir} Market order");
								PlaceMarketOrder(signalDir, Quantity);
							}
							else
							{
								/// Open a limit order using the open of the bar which matches the signal's post time
								double price = GetBarOpenPriceFromTime(0, lastSignalTime);
								if (price == -1)
									Log($"GetBarOpenPriceFromTime could not Open price of the bar associated with {lastSignalTime.ToString()}");
								else
								{
									Log($"Placing {signalDir} Limit order at price {price}");
									PlaceLimitOrder(signalDir, price, Quantity);
								}
							}
							lastTradeDir = signalDir;
							manualSignal = "None";
							
							/// Got this far, means we placed an order.  Hopefully ir goes thru fine, but either way, we 
							/// do not need to check trail on same tick, so return. This was actually a problem, where 
							/// it was trying to trail on a SL that we just cancelled but the thread hadn't completed yet.
							/// This is what was causing all the stops to be loeft behind after reversals.
							return;
						}
					}
				}
				#endregion
				
				#region TRAILING STOP - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
				if (Position.MarketPosition != MarketPosition.Flat  &&  (UseTrailingStop  ||  UseEoDTrail))
				{
					/// This section just looks at triggering trail, and expanding TP to new value (done once)
					if (!trailTriggered  &&  useTrailingStop)
					{
						if ((Position.MarketPosition == MarketPosition.Long  &&  High[0] >= AvePrice() + TS_ActivationTicks*TickSize)
						||  (Position.MarketPosition == MarketPosition.Short  &&  Low[0] <= AvePrice() - TS_ActivationTicks*TickSize))
						{
							trailTriggered = true;
							if (TS_TakeProfitTicks > TakeProfit3)
							{
								double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
								double tp3 = AvePrice() + m*TS_TakeProfitTicks*TickSize;
								Log($"TRAIL TRIGGERED: Sending one-time modification of TP3 to {tp3}");
								ModifyTakeProfit3(tp3);
							}
						}
					}
					
					/// EoD Trail supercedes regular trail
					if (UseEoDTrail  &&  Time[0].TimeOfDay >= TS_EoDStartTime.TimeOfDay)
					{
						/// This trail is in effect until the end of day / session, so really no need to change it back.  
						/// Even if we do not restart it each day, they are reset on first candle of session (6pm PST)
						useSimpleTrail = useTrailingStop = trailTriggered = true;
						simpleTicks = TS_EoDTicks;
					}
					if (useTrailingStop)
					{
						double newSL = GetNewTrailPrice(Position.MarketPosition.ToString());
						if (newSL != 0)
						{
							Log($"MODIFYING TRAILING STOPLOSS TO {newSL}");
							ModifyStopLoss(newSL);
						}
					}
				}
				#endregion
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		#region JACOB SIGNAL (Get signal from DB) - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		
		public enum FlipType
		{
			GammaFlip,
			DeltaFlip,
			ThetaFlip,
			VegaFlip
		}
		
		public class GreeksData
		{
			public DateTime Est_time { get; set; }
			public int gammaflip { get; set; }
			public int deltaflip { get; set; }
			public int thetaflip { get; set; }
			public int vegaflip { get; set; }
			public double total_call_volume_gamma { get; set; }
			public double total_put_volume_gamma { get; set; }
			public double total_call_volume_delta { get; set; }
			public double total_put_volume_delta { get; set; }
			public double total_call_volume_theta { get; set; }
			public double total_put_volume_theta { get; set; }
			public double total_call_volume_vega { get; set; }
			public double total_put_volume_vega { get; set; }
		}

		public class GreeksDataBackup
		{
			// Benoit??
			public DateTime Est_time { get; set; }
			public int gammaflip { get; set; }
			public int deltaflip { get; set; }
			public int thetaflip { get; set; }
			public int vegaflip { get; set; }
			public double total_call_volume_gamma { get; set; }
			public double total_put_volume_gamma { get; set; }
			public double total_call_volume_delta { get; set; }
			public double total_put_volume_delta { get; set; }
			public double total_call_volume_theta { get; set; }
			public double total_put_volume_theta { get; set; }
			public double total_call_volume_vega { get; set; }
			public double total_put_volume_vega { get; set; }
		}

		private void LogAllSignals()
		{
			FetchGreeksData(true, true);
			signalLogActivated = false;
		}
		
		private List<GreeksData> FetchGreeksData(bool debugOn=false, bool listAll=false)
		{
			string connectionString = "Host=mb.almanac.ai;Port=5432;Username=algotrader1;Password=**********;Database=accrue";
			string connectionStringBU = "Host=mb.almanac.ai;Port=5432;Username=algotrader1;Password=**********;Database=accrue";	// Benoit??
			List<GreeksData> sigData = new List<GreeksData>();

			string cln = ""; // for Log
			for (int l=0; l < 283; l++) cln = cln + ":";

			/// Open DB if not done yet.  Put below timeStrS because when we open, 
			/// we want to get all signals since 09:30 so we can set prevailing, even 
			/// if it's past that time when we start
			if (postgreSqlConnection == null)
			{
				Log($"OPENING DB...");
				postgreSqlConnection = new NpgsqlConnection(connectionString);
				postgreSqlConnection.Open();
			}
			
			/// Create the date string to send to DB, which seems to lag by almost 4 minutes, 
			/// so we need to offset by 4 just to be sure of getting [at least] one signal.
			string timeStrS, timeStrE;
			DateTime dtS, dtE;			/// date time  Start/End
			
			/// For any situation, begin with start and end time as 'current' bar, 
			/// timezone-adjusted.  Other adjustments made below.
			dtE = dtS = Time[0].AddHours(TimeZoneOffset);
			
			/// Log all signals for TODAY, not historical (though it *should* work)
			if (listAll)
			{
				/// We want to Log a list of all signals today.  Or, if it is before open
				/// of market, fetch signals for yesterday / last business day if weekend.
				timeStrS = dtS.ToString("yyyy-MM-dd");
				
				/// Create end time string for mindight tomorrow
				dtE = dtS.AddHours(24);
				timeStrE = dtE.ToString("yyyy-MM-dd");
				Log($"Loading all signals for today: {timeStrS} to {timeStrE}");
			}
			else if (isNewDay)	/// DB first new signal each day is @ 09:30
			{
				/// If we just opened the DB, get all signals since 09:30, to set prevailing
				/// This should work for historical, because Time[0] is bar being processed.
				dtS = new DateTime(dtS.Year, dtS.Month, dtS.Day, 9, 30, 0);
				
				/// If bar being processed is before 9:30, then nothing to do; no signal
				/// entries yet today.  The session run times for this bot should keep 
				/// this from ever happening, but just in case...
				if (Time[0].AddHours(TimeZoneOffset) < dtS)
				{
					Log($"Signal checked prior to open; aborting");
					return sigData;
				}
				/// Create start string for today, from 09:30
				timeStrS = dtS.ToString("yyyy-MM-dd HH:mm:ss");
				
				/// Create end time string, which should be 'Now' (current chart bar), 
				/// timezone-adjusted (dtE set above)
				timeStrE = dtE.ToString("yyyy-MM-dd HH:mm:ss");
				Log($"GETTING INITIAL GETTING SIGNALS FROM OPEN: {timeStrS} to {timeStrE}  (Time[0] = {Time[0].ToString()})");
				isNewDay = false;
			}
			/// Create the date string to send to DB, which seems to lag by almost 4 minutes, 
			/// so we need to offset by 4 just to be sure of getting [at least] one signal.
			else
			{
				/// Create start time string by Subtracting our max delay from 'current' bar
				dtS = dtS.AddSeconds(-MaxDelaySeconds);
				timeStrS = dtS.ToString("yyyy-MM-dd HH:mm:ss");
				
				/// End time is just TZ-adjusted 'current' bar (set above)
				timeStrE = dtE.ToString("yyyy-MM-dd HH:mm:ss");
				//Log($"Loading data from {timeStrS} to {timeStrE}");
			}

			bool dbg = false;
			string query = $@"
				SELECT 
					est_time, gammaflip, deltaflip, thetaflip, vegaflip,
					total_call_volume_gamma, total_put_volume_gamma,
					total_call_volume_theta, total_put_volume_theta,
					total_call_volume_vega, total_put_volume_vega,
					total_call_volume_delta, total_put_volume_delta,
					gamma_option_symbol, delta_option_symbol, theta_option_symbol, vega_option_symbol
				FROM spx_options5_analytics 
				WHERE est_time >= '{timeStrS}' AND est_time < '{timeStrE}'
				ORDER BY est_time;";

			// Benoit??
			string queryBU = $@"
				SELECT 
					est_time, gammaflip, deltaflip, thetaflip, vegaflip,
					total_call_volume_gamma, total_put_volume_gamma,
					total_call_volume_theta, total_put_volume_theta,
					total_call_volume_vega, total_put_volume_vega,
					total_call_volume_delta, total_put_volume_delta,
					gamma_option_symbol, delta_option_symbol, theta_option_symbol, vega_option_symbol
				FROM spx_options5_analytics 
				WHERE est_time >= '{timeStrS}' AND est_time < '{timeStrE}'
				ORDER BY est_time;";
			try
			{
				using (var cmd = new NpgsqlCommand(query, postgreSqlConnection))
				using (var reader = cmd.ExecuteReader())
				{
					while (reader.Read())
					{
						/*
						Print($"               Est_time = {reader.GetDateTime(0)}");
						Print($"               gammaflip = {reader.GetInt32(1)}");
						Print($"               deltaflip = {reader.GetInt32(2)}");
						Print($"               thetaflip = {reader.GetInt32(3)}");
						Print($"               vegaflip = {reader.GetInt32(4)}\n");
						*/
						var data = new GreeksData
						{
							Est_time = reader.GetDateTime(0),
							gammaflip = reader.GetInt32(1),
							deltaflip = reader.GetInt32(2),
							thetaflip = reader.GetInt32(3),
							vegaflip = reader.GetInt32(4),
							total_call_volume_gamma = reader.GetDouble(5),
							total_put_volume_gamma = reader.GetDouble(6),
							total_call_volume_theta = reader.GetDouble(7),
							total_put_volume_theta = reader.GetDouble(8),
							total_call_volume_vega = reader.GetDouble(9),
							total_put_volume_vega = reader.GetDouble(10),
							total_call_volume_delta = reader.GetDouble(11),
							total_put_volume_delta = reader.GetDouble(12)
						};
						//Print("               Adding to list");
						if ((TradeGamma  &&  data.gammaflip != 0)
						||  (TradeDelta  &&  data.deltaflip != 0)
						||  (TradeTheta  &&  data.thetaflip != 0)
						||  (TradeVega   &&  data.vegaflip != 0))
							dbg = true;
						sigData.Add(data);
					}
				}
			}
			catch (Exception ex)
			{
				Log($"                 Error fetching PostgreSQL data: {ex.Message}");
				Log($"Error fetching PostgreSQL data: {ex.Message}", LogLevel.Error);
			}
			
			if (sigData.Count > 0)
			{
				if (jacobDown)
				{
					jacobDown = false;
					//cln = ""; for (int l=0; l < 230; l++) cln = cln + ":";
					Log($"\n{cln}\n      DB SUCCESS: ENDING AT {DateTime.Now.AddHours(TimeZoneOffset).ToString("HH:mm:ss")} EST, DB QUERY: {timeStrS}  --  {timeStrE} RETURNED RESULTS\n{cln}\n\n");
				}
				//if (listAll  ||  (dbg  &&  State != State.Historical))
				//	Log($"                 Data Count = {sigData.Count}  EST Post Time = {sig.Est_time.ToString()}   EST Cur. Time = {DateTime.Now.AddHours(TimeZoneOffset).TimeOfDay.ToString()}.  Sorting...");
				
				sigData.Sort((a, b) => b.Est_time.CompareTo(a.Est_time));

				/// For debugging, we log all [today's] symbols when button is pressed -  -  -  -  -  -  -  -  -  -  -  -  -  -  -
				if (listAll  ||  debugOn)
				{
					if (listAll)
					{
						Log($"====================================");
						Log($"        Today's Jacob Signals");
						Log($"====================================");
					}
					else
						Log($"====================================");
	
					foreach (var sig in sigData)
					{
						if (sig.Est_time.Date != Time[0].Date)
						{
							Log($"ERROR Signal date ({sig.Est_time.Date.ToString()}) is not today ({Time[0].Date.ToString()})!");
							continue;
						}
						if ((TradeGamma  &&  sig.gammaflip != 0)  
						||  (TradeDelta  &&  sig.deltaflip != 0)  
						||  (TradeTheta  &&  sig.thetaflip != 0)  
						||  (TradeVega   &&  sig.vegaflip != 0))
							Log($"\n Post Time: {sig.Est_time.ToString()}");
						if (TradeGamma  &&  sig.gammaflip != 0)
						{
							Log($" GammaFlip : {sig.gammaflip}");
				            Log($" Gamma Call Vol : {sig.total_call_volume_gamma}");
				            Log($" Gamma Put  Vol : {sig.total_put_volume_gamma}");
						}
						if (TradeDelta  &&  sig.deltaflip != 0)
						{
							Log($" DeltaFlip : {sig.deltaflip}");
				            Log($" Delta Call Vol : {sig.total_call_volume_delta}");
				            Log($" Delta Put  Vol : {sig.total_put_volume_delta}");
						}
						if (TradeTheta  &&  sig.thetaflip != 0)
						{
							Log($" ThetaFlip : {sig.thetaflip}");
				            Log($" Theta Call Vol : {sig.total_call_volume_theta}");
				            Log($" Theta Put  Vol : {sig.total_put_volume_theta}");
						}
						if (TradeVega  &&  sig.vegaflip != 0)
						{
							Log($" VegaFlip  : {sig.vegaflip}");
				            Log($" Vega Call Vol : {sig.total_call_volume_vega}");
				            Log($" Vega Put  Vol : {sig.total_put_volume_vega}\n");
						}
							
	
					}
					Log($"\n====================================");
				}  /// -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -
			}
			else if (State != State.Historical)
			{
				DateTime nowEST = DateTime.Now.AddHours(TimeZoneOffset);
				TimeSpan hour = DateTime.Now - DateTime.Now.AddHours(-1);
				if (!jacobDown)
				{
					jacobDown = true;
					dbFailureTime = nowEST;		// NOT SURE THIS SHOULD BE REPORTING FAILURE ANYMORE -CHECK INTO IT
					Log($"\n{cln}\n  DB FAILURE: STARTING AT {dbFailureTime.ToString("HH:mm:ss")} EST, NO ENTRIES FETCHED FROM DB QUERY: {timeStrS}  --  {timeStrE}\n{cln}\n\n");
				}
				else if (DateTime.Now.AddHours(TimeZoneOffset) - dbFailureTime > hour)
				{
					dbFailureTime = nowEST;
					Log($"\n{cln}\n  DB FAILURE UPDATE: STILL RECEIVING NO RESULTS FROM QUERY 60 MINUTES LATER @ {dbFailureTime.ToString("HH:mm:ss")}\n{cln}\n\n");
				}
			}
			return sigData;
		}
		
		private void ParseSignals()
		{
			/// Check if there are any signals in the list
			if (signalList == null  ||  signalList.Count < 1)
			{
				Log($"No data found.  signalList = {signalList}, count = {signalList.Count}");
				return;
			}
			
			string sigDir = "None";
			foreach (var sig in signalList)
			{
				if (sig.Est_time.Date != Time[0].Date)
				{
					Log($"ERROR Signal date ({sig.Est_time.Date.ToString()}) is not today!");
					continue;
				}
				if (TradeGamma  &&  sig.gammaflip != 0)	
					sigDir = (sig.gammaflip == 1) ? "Long" :"Short";
				else if (TradeDelta  &&  sig.deltaflip != 0)
					sigDir = (sig.deltaflip == 1) ? "Long" :"Short";
				else if (TradeTheta  &&  sig.thetaflip != 0)
					sigDir = (sig.thetaflip == 1) ? "Long" :"Short";
				else if (TradeVega   &&  sig.vegaflip  != 0)
					sigDir = (sig.vegaflip == 1) ? "Long" :"Short";
				
				// On the first signal of the day, determine prevailing direction by volume
	            if (lastTradeDir == "None")
				{
		            if (ToTime(sig.Est_time) == ToTime(9, 30, 0))
					{
						if (TradeGamma)
						{
							lastTradeDir = (sig.total_call_volume_gamma > sig.total_put_volume_gamma) ? "Long" : "Short";
			 				Log($"Prevailing Dir set to {lastTradeDir} on Gamma; call vol = {sig.total_call_volume_gamma}, put vol = {sig.total_put_volume_gamma}");
						}
						else if (TradeDelta)
						{
							lastTradeDir = (sig.total_call_volume_delta > sig.total_put_volume_delta) ? "Long" : "Short";
			 				Log($"Prevailing Dir set to {lastTradeDir} on Delta; call vol = {sig.total_call_volume_delta}, put vol = {sig.total_put_volume_delta}");
						}
						else if (TradeTheta)
						{
							lastTradeDir = (sig.total_call_volume_theta > sig.total_put_volume_theta) ? "Long" : "Short";
			 				Log($"Prevailing Dir set to {lastTradeDir} on Theta; call vol = {sig.total_call_volume_theta}, put vol = {sig.total_put_volume_theta}");
						}
						else if (TradeVega)
						{
							lastTradeDir = (sig.total_call_volume_vega > sig.total_put_volume_vega) ? "Long" : "Short";
			 				Log($"Prevailing Dir set to {lastTradeDir} on Vega; call vol = {sig.total_call_volume_vega}, put vol = {sig.total_put_volume_vega}");
						}
						if (lastTradeDir == "None")
			 				Log($"Prevailing Dir NOT set by 09:30 candle");
					}
					/// If EA was just started, but it's [well] past 9:30, we need to set lastTradeDir as the most recent signal...
					else if (sigDir != "None")
					{
						lastTradeDir = sigDir;
		 				Log($"lastTradeDir set to {lastTradeDir} on signal from {sig.Est_time.TimeOfDay.ToString()}");
					}
				}
				/// If signal is more recent than last, we have a winner!
				if (sigDir != "None"  &&  sig.Est_time > lastSignalTime)
				{
					Log($"      New {sigDir} Jacob signal found!");

					if (State != State.Historical)
						Print($"      EST Post Time / Current Time : {sig.Est_time.ToString()} / {DateTime.Now.AddHours(TimeZoneOffset).ToString("HH:mm:ss")}");
//						Print($"      EST Post Time / Current Time : {sig.Est_time.ToString()} / {DateTime.Now.AddHours(TimeZoneOffset).TimeOfDay.ToString("HH:mm:ss")}");
						// This line was crashing the program.  I am suspecting it is because the ToString() param does not mesh with .TimeOfDay
					else
						Print($"      EST Post Time / Hist. Time : {sig.Est_time.ToString()} / {Time[0].AddHours(TimeZoneOffset).ToString("HH:mm:ss")}");

					if (TradeGamma  &&  sig.gammaflip != 0)	
						Log($"      Gamma Dir / Call / Put Vol : {sig.gammaflip} / {sig.total_call_volume_gamma} / {sig.total_put_volume_gamma}");
					if (TradeDelta  &&  sig.deltaflip != 0)	
						Log($"      Delta Dir / Call / Put Vol : {sig.deltaflip} / {sig.total_call_volume_delta} / {sig.total_put_volume_delta}");
					if (TradeTheta  &&  sig.thetaflip != 0)	
						Log($"      Theta Dir / Call / Put Vol : {sig.thetaflip} / {sig.total_call_volume_theta} / {sig.total_put_volume_theta}");
					if (TradeVega   &&  sig.vegaflip  != 0)	
						Log($"       Vega Dir / Call / Put Vol : {sig.vegaflip} / {sig.total_call_volume_vega} / {sig.total_put_volume_vega}");
					
					signalDir = sigDir;
					lastSignalTime = sig.Est_time;
					firstSignalRecevied = true;
					break;
				}
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		#endregion
	
		#region TRADE MANAGEMENT  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void PlaceMarketOrder(string dir, int qty, string name="Entry")
		{
			Log($"Placing market order, dir = {dir}, qty = {qty}, name = {name}");
			
			// THIS just gets in it's own way, and  will
			if (Position.MarketPosition != MarketPosition.Flat  &&  Position.MarketPosition.ToString() != dir)
			{
				Log($"Existing position is {Position.MarketPosition.ToString()}, so closing position first");
				string id = (manualSignal == "None") ? "CloseOnRev" : "ManExit";
				ClosePosition(id);
				
				// Assume the close worked; need to reset this NOW because it's multi-threaded
				stopsSubmitted = false;
			}
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			double price = (dir == "Long") ? GetCurrentAsk() : GetCurrentBid();
			Log($"Placing {dir} Market order named '{name}' for {qty} contract(s). Current price is: {price}");
			SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		private void PlaceLimitOrder(string dir, double price, int qty, string name="Entry")
		{
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			if ((dir == "Long"  &&  GetCurrentAsk() <= price)
			||  (dir == "Short"  &&  GetCurrentBid() >= price))
			{
					Log($"Current price better than signal time price; placing market order");
					PlaceMarketOrder(dir, qty);
					lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
					return;
			}
			
			/// If there is a current pending entryOrder, cancel it
			if (entryOrder != null  &&  entryOrder.OrderType == OrderType.Limit)
			{
				Log($"Already in {entryOrder.OrderAction.ToString()} limit order; Cancelling it");
				CancelOrder(entryOrder);
				entryOrder = null;
			}
			
			/// NOW... enter new limit order
			Log($"Placing {dir} Limit order named {name} for {qty} contract(s) @ {price}");
			SubmitOrderUnmanaged(1, action, OrderType.Limit, qty, price, 0, "", "Entry");
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		private void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return;
			
			if (qty == 0)	qty = Position.Quantity;
			if (dir == "")	dir = Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;
			
			if (qty == Position.Quantity)
			{
				if (slOrder != null  ||  tpOrder3 != null)
				{
					Log($"Before closing all contracts, slOrder != null  ||  tpOrder3 != null, so Cancelling Stops");
					CancelStops();
					//inContinuationTrade = false;	// assume trade closes below - SHOULD HAPPEN IN OnExecUpdate
				}
			}
			Log($"Closing Position: name = {name}, dir = {dir}, qty = {qty}");
			SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}

		private void ModifyStopLoss(double newSL)
		{
			/// If there is no existing SL, we must place the order
			if (slOrder == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (tpOrder3 != null) ? tpOrder3.Oco : "";
				Log($"         WARNING WARNING (slOrder == null)  ABORTING MODIFY");
				//Log($"    WARNING: No SL existed (slOrder == null) on Long position, so placing new SL order, price @ {newSL}");
				//SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, newSL, oco, "StopLoss");
			}
			else
			{
				Log($"slOrder Action = {slOrder.OrderAction.ToString()} - Moving SL to new price @ {newSL} (either trail or move to breakeven)");
				ChangeOrder(slOrder, Position.Quantity, 0, newSL);
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}

		private void ModifyTakeProfit3(double newTP)
		{
			/// If there is no existing TP (e.g. No TakeProfit3 set, but TS_TakeProfitTicks set), then we must place the order
			if (tpOrder3 == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (slOrder != null) ? slOrder.Oco : "";
				Log($"         WARNING WARNING (tpOrder3 == null)  ABORTING MODIFY");
				//Log($    WARNING: No TP existed on Long position, so placing new TP order, price @ {newTP}");
				//SubmitOrderUnmanaged(0, action, OrderType.Limit, quantityTP3, newTP, 0, oco, "TakeProfit");
			}
			else
			{
				Log($"Moving TP3 to new price @ {newTP}");
				ChangeOrder(tpOrder3, quantityTP3, newTP, 0);
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		private bool CancelStops()
		{
			bool cancelled = false;
			if (slOrder != null)
			{
				Log($"Cancelling slOrder");
				CancelOrder(slOrder);
				slOrder = null;
				cancelled = true;
			}
			if (tpOrder1 != null)
			{
				Log($"Cancelling tpOrder1");
				CancelOrder(tpOrder1);
				tpOrder1 = null;
				cancelled = true;
			}
			if (tpOrder2 != null)
			{
				Log($"Cancelling tpOrder2");
				CancelOrder(tpOrder2);
				tpOrder2 = null;
				cancelled = true;
			}
			if (tpOrder3 != null)
			{
				Log($"Cancelling tpOrder3");
				CancelOrder(tpOrder3);
				tpOrder3 = null;
				cancelled = true;
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
			return cancelled;
		}
		
		private double AvePrice()
		{
			double avePrice = Position.AveragePrice;
			
			/// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				avePrice = prc;
			}
			return(avePrice);
		}
		
		private double GetBarOpenPriceFromTime(int bip, DateTime time)
		{
			time = time.AddHours(-TimeZoneOffset);
			for (int i = 0; i < 100 ; i++)
			{
				/// Must be >, not >=, because ninja candles are on close time, not open
				//Log($"Times[bip][{i}] = {Times[bip][i]}, Times[bip][{i+1}] = {Times[bip][i+1]}; Time to compare to: {time}");
				if (Times[bip][i] > time  &&  Times[bip][i+1] <= time)
					return Opens[bip][i];
			}
			return -1;
		}
		#endregion
		
		#region TRAIL & SWING - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		/// Return new trailing stoploss value, or zero if none/unchanged
		private double GetNewTrailPrice(string direction)
		{
			double priceHLC = Close[0];
			double oldSL = 0;
			double newSL = 0;
			if (Position.MarketPosition.ToString() != direction  ||  !trailTriggered)
				return newSL;
			
			/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
			if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
			{
				oldSL = slOrder.StopPrice;
				//Log($"Old Stop Price = {oldSL}");
			}

			double entryPrice = AvePrice();
			double ticksInProfit = (direction == "Short") ? (entryPrice - Close[0]) / TickSize : (Close[0] - entryPrice) / TickSize;
			double ticksToOldSL = 0;
			priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
			if (oldSL != 0)
				ticksToOldSL = (direction == "Short") ? (oldSL - priceHLC) / TickSize : (priceHLC - oldSL) / TickSize;

			/// Set up Min and Max Ticks according to how much we are in profit
			int minDist = TS_MinTicks;
			int maxDist = TS_MaxTicks;
			if (TS_ReAdjustAtTicks2 > 0  &&  ticksInProfit >= TS_ReAdjustAtTicks2)
			{
				minDist = TS_MinTicks2;
				maxDist = TS_MaxTicks2;
			}
			else if (TS_ReAdjustAtTicks1 > 0  &&  ticksInProfit >= TS_ReAdjustAtTicks1)
			{
				minDist = TS_MinTicks1;
				maxDist = TS_MaxTicks1;
			}
				
			if (useSimpleTrail)
			{
				/// ticksToOldSL is zero if there is no old SL
				if (ticksToOldSL > simpleTicks)
				{
					if (direction == "Short") 
						newSL = priceHLC + simpleTicks*TickSize;
					else
						newSL = priceHLC - simpleTicks*TickSize;
					Log($"Simple trail: OldSL = {oldSL}, NewSL = {newSL}");
				}
				return newSL;
			}
			
			/// Loop through swing strengths from 5 down to 1, looking for 
			/// the highest strength that fits the min & max requirement
			/// Yes, this seems brute force, but c'est la vie!
			///
			/// No, screw that.  It seems like if we just look at strength=1, 
			/// in the small range between TS_MinTicks & TS_MaxTicks, and use 
			/// the first one we find, it will often actually have a better 
			/// strength.  Lets test it out...
			///
			/// Tried to write it do potentially do the original idea, but 
			/// it's pretty complex, so if this seems to work out fine, 
			/// functionally what we want, I should simplify this.  
			///
			/// And if not, need to make modifications as well...
			int i, j, ss;
			double distToFractal, slPrice;
			for (i=1; newSL == 0  &&  i < firstEntryBarsAgo; i++)
			{
				/// Until proven otherwise, assume the candle we are testing (index i) 
				/// DOES indeed the have the fractal H/L we seek, so start slPrice as H/L
				slPrice = (direction == "Short") ? High[i] - TS_OffsetTicks*TickSize : Low[i] + TS_OffsetTicks*TickSize;
				
				/// If slPrice wouldn't even move the stop, skip to next candle
				if (oldSL != 0)
					if ((direction == "Short") ? (oldSL <= slPrice) : (oldSL >= slPrice))
						continue;

				// Get the distance from the H/L (or Close) to the alleged fractal
				priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
				distToFractal = (direction == "Short") ? (slPrice - priceHLC) / TickSize : (priceHLC - slPrice) / TickSize;
				
				// ss = swing strength; here hard-coded to 1
				for (ss=1; newSL == 0  &&  ss > 0; ss--)
				{
					for (j = 1; newSL == 0  &&  j <= ss; j++)
					{
						if (direction == "Short")
						{
							/// Not a Swing High; try next candle
							if (High[i+j] > High[i]  ||  High[i-j] > High[i])
								slPrice = 0;
						}
						else if (direction == "Long")
						{
							/// These aren't the droids you're looking for...
							if (Low[i+j] < Low[i]  ||  Low[i-j] < Low[i])
								slPrice = 0;
						}
						/// Found a LL or HH, so this ain't it; check next smaller SS
						if (slPrice == 0)
							break;	// out of for(j...) loop
						
						/// Found a fractal; see if its far enough and not too far from price
						else if (distToFractal >= minDist  &&  distToFractal <= maxDist)
						{
							if (oldSL == 0 
							|| (direction == "Short"  &&  slPrice < oldSL) 
							|| (direction == "Long"   &&  slPrice > oldSL))
							{
								newSL = slPrice;
								Log($"Found SwingStrength(1) fractal @ {Time[i].ToString()} (index {i}); set newSL = {newSL}  (oldSL = {oldSL})");
							}
						}
					}
				}
			}
			
			/// If we found no fractal, but our current stop is > max, we need to move it
			if (newSL == 0  &&  (ticksToOldSL > maxDist  ||  oldSL == 0))
			{
				Log($"No fractal found since trade opened; checking for max distance");
				priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
				if (direction == "Short") 
				{
					newSL = priceHLC + maxDist*TickSize;
					if (newSL >= oldSL)	newSL = 0;
				}
				else
				{
					newSL = priceHLC - maxDist*TickSize;
					if (newSL <= oldSL)	newSL = 0;
				}
				if (newSL != 0)
				{
					if (oldSL == 0)
						Log($"old SL absent, so moving to {newSL}");
					else
						Log($"old SL too distant (ticksToOldSL = {ticksToOldSL}, maxDist = {maxDist}), so moving to {newSL}");
				}
			}
			return newSL;
		}
		
		/// This is used to get the last swing H/L for a breakout price used for continuation trades
		private double GetLastSignificantSwingPrice(string direction, int min=MIN_LOOKBACK, int add=ADD_LOOKBACK, int max=MAX_LOOKBACK)
		{
			if (direction != "Long"  &&  direction != "Short")
				return -1;
			///
			/// fetch the price of the last most significant swing H/L in the given direction
			/// (we use the term "Fractal" and "Swing H/L" interchangeably
			///

			/// To initialize, we want to use the last Strength(1) SwingHL
			int i, j;
			double initPrice = (direction == "Long") ? High[1] : Low[1];
			double price = (direction == "Long") ? High[1] : Low[1];
			int lastIdx = 1;
			for (i=1; i < 50; i++)
			{
				if (direction == "Short"  &&  Low[i+1] > Low[i]  &&  Low[i-1] > Low[i])
				{
					initPrice = price = Low[i];
					lastIdx = i;
					break;
				}
				else if (direction == "Long"  &&  High[i+1] < High[i]  &&  High[i-1] < High[i])
				{
					initPrice = price = High[i];
					lastIdx = i;
					break;
				}
			}
			//if (i == 50)	Log($"{direction} : Could not find Strength(1) fractal in last 50 bars; using index[1] H/L @ {price}");
			//else			Log($"{direction} : Found Strength(1) fractal at bar[{lastIdx}] ({Time[lastIdx].ToString()}) @ {price}");
			
			
			//Log($"Finding last {direction} fractal price...");
			/// Use hard-coded (for now at least):
			/// We will use an initial lookback of 61 bars, because often there is a volume/price 
			/// spike 60 minutes before 9:30 open.  Then we will keep looking back 20 bars at a 
			/// time until we do not find a more extreme swing price.
			int lookBack = MIN_LOOKBACK;
			int additional = 20;
			if (CurrentBars[0] - ContStrength < lookBack)
			{
				//Log($"GetLastSignificantSwingPrice: There are only {CurrentBars[0]} bars on the chart, so must reduce look back period");
				lookBack = CurrentBars[0] - ContStrength;
			}
			
			int start = ContStrength + 1;
			int lb = lookBack;
			bool furtherFound = true;
			while (furtherFound  &&  lb < MAX_LOOKBACK)
			{
				/// iterate over last lookBack candles seeking fractal levels
				for (j = start; j < lb; j++)
				{
					/// Test candle 'j' to see if it qualifies as Swing
					bool newFound = true;
					for (i = 1; i <= ContStrength; i++)
					{
						if (direction == "Long")
						{
							if (High[j+i] > High[j]  ||  High[j-i] > High[j])	
							{
								newFound = false;
								break;
							}
						}
						else if (direction == "Short")
						{
							if (Low[j+i] < Low[j]  ||  Low[j-i] < Low[j])
							{
								newFound = false;
								break;
							}
						}
					}
					
					/// If it DID qualify and is more extreme than previous, save it
					if (newFound)
					{
						/// Save last swing index, even if it's not a new H/L (see below)
						//Log($"Continuation Fractal Found @ index {j} ({Time[j].ToString()})");
						if (direction == "Long"  &&  High[j] > price)
						{
							lastIdx = j;
							price = High[j];
							//Log($"{direction} : Fractal @ index {j} ({Time[j].ToString()}) also marks new High @ {price}");
						}
						else if (direction == "Short"  &&  Low[j] < price)
						{
							lastIdx = j;
							price = Low[j];
							//Log($"{direction} : Fractal @ index {j} ({Time[j].ToString()}) also marks new Low @ {price}");
						}
						/// No need to check the next 'ContStrength' candles; we know they are lower/higher
						j += ContStrength;
					}
				}
				
				/// If the lastIdx was more than (lookBack - additional) back, 
				/// then look back a bit further for a more extreme H/L
				if (lastIdx > lb - additional)
				{
					start = lb;
					lb += additional;
					//Log($"{direction} : Last fractal index @ {lastIdx} was close to end, so look back {additional} bars further; start = {start}, new limit = {lb}");
				}
				/// If the lastIdx found was more than 20 candles forward 
				/// the earliest one checked, then just use this fractal point
				else
					furtherFound = false;
			}
			/// If whatever fractal found was not higher/lower than H/L[1], then we are 
			/// actively heading up/down and need to wait for at least a 1-bar retrace
			if (direction == "Short")
			{
				if (price > initPrice)
				{
					//Log($"{direction} : Fractal low found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is higher than last Low Swing(1) @ {initPrice}, so use that price");
					price = initPrice;
				}
				else if (price > Low[1])
				{
					//Log($"{direction} : Fractal low found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is higher than Low[1] @ {Low[1]}, so use that price");
					price = Low[1];
				}
			}
			else	// Long
			{
				if (price < initPrice)
				{
					//Log($"{direction} : Fractal high found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is lower than last High Swing(1) @ {initPrice}, so use that price");
					price = initPrice;
				}
				else if (price < High[1])
				{
					//Log($"{direction} : Fractal high found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is lower than High[1] @ {High[1]}, so use that price");
					price = Low[1];
				}
			}
			//Log($"{direction} : Found Fractal price level @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) = {price}");
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
			return price;
		}
		#endregion

		private bool TradeFiltersOkay(string direction)
		{
			/// Enter a trade [set] if all enabled filters are satisfied
			
			/// Do not filter a manual command to enter a position
			if (manualSignal != "None")
				return(true);
			
			if (direction != "Long"  &&  direction != "Short")
			{
				Log($"Direction passed into TradeFiltersOkay ({direction}) must be 'Long' or 'Short'; Aborting");
				return false;
			}
			
			/// Save the last signal direction (even if actual trade is blocked by filters)
			lastTradeDir = direction;
			
			if (direction == "Long")
			{
				direction = "None";
				if (!longOn)
					Log($"Long trade disabled; Aborting entry");
				else if (Position.MarketPosition == MarketPosition.Long)
					Log($"Long trade already open; Aborting entry");
				else
					direction = "Long";
			}
			else if (direction == "Short")
			{
				direction = "None";
				if (!shortOn)
					Log($"Short trade disabled; Aborting entry");
				else if (Position.MarketPosition == MarketPosition.Short)
					Log($"Short trade already open; Aborting entry");
				else
					direction = "Short";
			}
			
			if (direction == "None")
				return false;

			if (VOLMA(Closes[0], AveVolumePeriod)[1] < MinAveVolume)
			{
				Log($"Volume insufficient ({VOLMA(Closes[0], AveVolumePeriod)[1]} vs. {MinAveVolume} minimum); Aborting {direction} entry");
				return false;
			}
			if (!CheckSession())
			{
				Log($"Out of session; Aborting {direction} entry");
				return false;
			}
			return true;
		}
		
		private bool CheckSession()
		{
			/// Check trading sessions; return false if out-of-session

			if (!LimitTradingHours)
				return true;
			
			bool hoursOk = false; 
			if (LimitTradingHours)
			{
				if (StartTime.TimeOfDay < EndTime.TimeOfDay)
				{
					if (Time[0].TimeOfDay >= StartTime.TimeOfDay  &&  Time[0].TimeOfDay < EndTime.TimeOfDay)
						hoursOk = true;
				}
				else if (StartTime.TimeOfDay > EndTime.TimeOfDay)
				{
					if (Time[0].TimeOfDay >= StartTime.TimeOfDay  ||  Time[0].TimeOfDay < EndTime.TimeOfDay)
						hoursOk = true;
				}
				else // (StartTime1.TimeOfDay == EndTime1.TimeOfDay)
				{
					Log($"Start Time 1 is the same as End Time 1; Trading [always] approved");
						hoursOk = true;
				}
			}
			/// Draw a vertical line one candle back at start of day
			if (!inSession  &&  hoursOk)
				Draw.VerticalLine(this, "SoD" + DateTime.Now.TimeOfDay, Time[1], Brushes.Yellow);
			return hoursOk;
		}
		
		private void ManageOCD()
		{
			/// Manage the on-chart display
			string text = $"\n\n Strategy Name            :   {StrategyName} v{VERSION}, {UniqueID}";
			text += $"\n Trade Session Active   :   {inSession}";
			if (lastTradeDir != "None"  &&  !firstSignalRecevied)
				text += $"\n Prevailing Dir @ Open :   {lastTradeDir}";
			else
				text += $"\n Last Signal Direction   :   {lastTradeDir}";

			if (ContTradesMode != ContTradesModeEnum.Disabled  &&  inSession)
			{
				text += $"\n\n Cont Trades Enabled   :   {contTradesEnabled}";
				text += $"\n Continuation Trades    :   {contTradesTaken}";
				text += $"\n Swing Price Long         :   {((lastSigSwingPriceL == -1) ? "None" : lastSigSwingPriceL.ToString("F2"))}";
				text += $"\n Swing Price Short        :   {((lastSigSwingPriceS == -1) ? "None" : lastSigSwingPriceS.ToString("F2"))}";
			}
			
			if (useTrailingStop  &&  !useSimpleTrail)
				text += $"\n Trail Triggered              :   {trailTriggered}";
			
			if (DailyMinTarget > 0  ||  DailyMaxLoss > 0)
				text += $"\n Daily Limit Hit              :   {dailyLimitHit}";

			if (lastFetchTime != DateTime.MinValue  &&  CheckSeconds > 2)
			{
				TimeSpan remaining = lastFetchTime.AddSeconds(CheckSeconds) - DateTime.Now;
				text += $"\n\n Next Signal Check in    :   {remaining.ToString("mm:ss")}";
			}

			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			/// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string id = StrategyName + " " + UniqueID;
			
			DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
			string dateStr = (State == State.Historical) ? date.ToString() : date.ToString("HH:mm:ss");
			
			if (lastCaller != memberName)
			{
				Print($"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n");
				Print(message);
			}
			else if (lastLogMsg != message)
			{
				if (lastLogTime != dateStr)	// Output just time if diff time but not new caller
					Print(message + $"   ( {dateStr} )");
				else
					Print(message);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}			
		
		#region BUTTONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			Log("\n");
			bool lWasOn = longOn;
			bool sWasOn = shortOn;
			if (button.Name == "enableLongButton")
			{
				if (longOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longOn = false;
					Log("\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longOn = true;
					Log("\nLong entries Enabled!");
				}
			}
			else if (button.Name == "enableShortButton")
			{
				if (shortOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortOn = false;
					Log("\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortOn = true;
					Log("\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
				Log("\nUser-Instigated Close of All Open Strategy Positions, but nothing open..");
				else
				{
					Log("\n\n=====================================================");
					Log("User-Instigated Close of All Open Strategy Positions");
					Log("=====================================================\n");
					ClosePosition();
				}
			}
			else if (button.Name == "logSignalsButton")
			{
				FetchGreeksData(true, true);
				//signalLogActivated = true;
				//TriggerCustomEvent(LogAllSignals);
			}
			if (button.Name == "manualEnterLong")
			{
				Log($"    ....MANUAL LONG ENTRY ON #{UniqueID}....");
				if (Position.MarketPosition != MarketPosition.Long)
					manualSignal = "Long";
				else
					Log($"    ....But already in Long trade; aborting request....");
			}
			else if (button.Name == "manualEnterShort")
			{
				Log($"    ....MANUAL SHORT ENTRY ON #{UniqueID}....");
				if (Position.MarketPosition != MarketPosition.Short)
					manualSignal = "Short";
				else
					Log($"    ....But already in Short trade; aborting request....");
			}
			
			if (!longOn  &&  !shortOn)
				if (lWasOn  ||  sWasOn)
					Log("ALL ENTRIES DISABLED!");
			else if (longOn  &&  shortOn)
				if (!lWasOn  ||  !sWasOn)
					Log("ALL ENTRIES ENABLED!");
			Log("\n");

			entriesDisabled = !(longOn  ||  shortOn);
		}
		
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
				return;
		
			myGrid = new System.Windows.Controls.Grid
			{
				Name = "MyCustomGrid",
				HorizontalAlignment = HorizontalAlignment.Left,
				VerticalAlignment = VerticalAlignment.Bottom,
			};
	 	 
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.ColumnDefinitions[0].Width = new GridLength(80);
			myGrid.ColumnDefinitions[1].Width = new GridLength(80);
			myGrid.ColumnDefinitions[2].Width = new GridLength(80);
			myGrid.RowDefinitions[0].Height = new GridLength(25);
			myGrid.RowDefinitions[1].Height = new GridLength(25);
	 
			enableLongButton = new System.Windows.Controls.Button
			{
			  	Name = "enableLongButton",
			  	Content = "Long Enabled",
			  	Foreground = Brushes.White,
			  	Background = Brushes.Green,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
			
			enableShortButton = new System.Windows.Controls.Button
			{
			  	Name = "enableShortButton",
			  	Content = "Short Enabled",
			  	Foreground = Brushes.White,
			  	Background = Brushes.Firebrick,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
	 
			exitButton = new System.Windows.Controls.Button
			{
			  	Name = "exitButton",
			  	Content = "Exit All",
			  	Foreground = Brushes.White,
			  	Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
			
			logSignalsButton = new System.Windows.Controls.Button
			{
			  	Name = "logSignalsButton",
			  	Content = "Log Signals",
			  	Foreground = Brushes.White,
			  	Background = Brushes.Blue,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
			
			manualEnterLong = new System.Windows.Controls.Button
			{
			  	Name = "manualEnterLong",
			  	Content = "Enter Long",
			  	Foreground = Brushes.Black,
			  	Background = Brushes.Lime,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
			
			manualEnterShort = new System.Windows.Controls.Button
			{
			  	Name = "manualEnterShort",
			  	Content = "Enter Short",
			  	Foreground = Brushes.White,
			  	Background = Brushes.Crimson,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
			
			enableLongButton.Click += OnMyButtonClick;
			enableShortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
			logSignalsButton.Click += OnMyButtonClick;
			manualEnterLong.Click += OnMyButtonClick;
			manualEnterShort.Click += OnMyButtonClick;
			
			System.Windows.Controls.Grid.SetColumn(enableShortButton, 0);
			System.Windows.Controls.Grid.SetRow(enableShortButton, 1);
			System.Windows.Controls.Grid.SetColumn(enableLongButton, 0);
			System.Windows.Controls.Grid.SetRow(enableLongButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 1);
			System.Windows.Controls.Grid.SetRow(exitButton, 0);
			System.Windows.Controls.Grid.SetColumn(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetRow(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetColumn(manualEnterLong, 2);
			System.Windows.Controls.Grid.SetRow(manualEnterLong, 0);
			System.Windows.Controls.Grid.SetColumn(manualEnterShort, 2);
			System.Windows.Controls.Grid.SetRow(manualEnterShort, 1);
			
			myGrid.Children.Add(enableLongButton);
			myGrid.Children.Add(enableShortButton);
			myGrid.Children.Add(exitButton);
			myGrid.Children.Add(logSignalsButton);
			myGrid.Children.Add(manualEnterLong);
			myGrid.Children.Add(manualEnterShort);

			UserControlCollection.Add(myGrid);
		}
		
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
			{
				if (enableLongButton != null)
				{
					myGrid.Children.Remove(enableLongButton);
					enableLongButton.Click -= OnMyButtonClick;
					enableLongButton = null;
				}
				if (enableShortButton != null)
				{
					myGrid.Children.Remove(enableShortButton);
					enableShortButton.Click -= OnMyButtonClick;
					enableShortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
				if (logSignalsButton != null)
				{
					myGrid.Children.Remove(logSignalsButton);
					logSignalsButton.Click -= OnMyButtonClick;
					logSignalsButton = null;
				}
				if (manualEnterLong != null)
				{
					myGrid.Children.Remove(manualEnterLong);
					manualEnterLong.Click -= OnMyButtonClick;
					manualEnterLong = null;
				}
				if (manualEnterShort != null)
				{
					myGrid.Children.Remove(manualEnterShort);
					manualEnterShort.Click -= OnMyButtonClick;
					manualEnterShort = null;
				}
			}
		}
		#endregion

		#region PROPERTIES  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Order Quantity", Description = "The initial number of contracts to place per order", Order=0, GroupName = "1. Entry")]
		public int Quantity
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Trade Gamma", Description = "Trade Gamma Signals", Order=3, GroupName = "1. Entry")]
		public bool TradeGamma
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Trade Delta", Description = "Trade Delta Signals", Order=6, GroupName = "1. Entry")]
		public bool TradeDelta
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Trade Theta", Description = "Trade Theta Signals", Order=9, GroupName = "1. Entry")]
		public bool TradeTheta
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Trade Vega", Description = "Trade Vega Signals", Order=12, GroupName = "1. Entry")]
		public bool TradeVega
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Last Trade Direction", Description = "Override for initial Last Trade Direction", Order=15, GroupName = "1. Entry")]
		public string LastTradeDir
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 300)]
		[Display(Name = "Check URL Interval", Description = "How many seconds between URL signal checks", Order=18, GroupName = "1. Entry")]
		public int CheckSeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 900)]
		[Display(Name = "Signal Delay Max Seconds", Description = "Maximum seconds allowed between signal time stamp and entry time", Order=21, GroupName = "1. Entry")]
		public int MaxDelaySeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the signal PostTime to compare with local time", Order=24, GroupName = "1. Entry")]
		public int TimeZoneOffset
		{ get; set; }

		
		
		[NinjaScriptProperty]
		[Display(Name = "Continuation Trades Mode", Description = "How continuation trades are handled", Order=0, GroupName = "2. ReEntry")]
		public ContTradesModeEnum ContTradesMode { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "BiDirectional", Description = "If no previous trade / prevailing direction, trade either breakout", Order=3, GroupName = "2. ReEntry")]
		public bool BiDirectional
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Continuation Trades Number", Description = "Number of continuation trades to take", Order=6, GroupName = "2. ReEntry")]
		public ContTradesNumberEnum ContTradesNum { get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Continuation Swing Strength", Description = "Swing ind setting for H/L on Prevailing & Continuation trades", Order=9, GroupName = "2. ReEntry")]
		public int ContStrength
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Require MA position", Description = "Require fast EMA above slow for long cont. trade and vice versa", Order=12, GroupName = "2. ReEntry")]
		public bool ContReqMAPosition
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(2, int.MaxValue)]
		[Display(Name = "Slow EMA Period", Description = "Period parameter of Slow EMA", Order=13, GroupName = "2. ReEntry")]
		public int SlowEMA_Period
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Fast EMA Period", Description = "Period parameter of Fast EMA", Order=14, GroupName = "2. ReEntry")]
		public int FastEMA_Period
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Activation Time", Description = "Start continuation trades after given time?", Order=15, GroupName = "2. ReEntry")]
		public bool UseContStartTime
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Time to enable continuation trades", Order=16, GroupName = "2. ReEntry")]
		public DateTime ContStartTime
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Deactivation Time", Description = "Halt continuation trades at given time?", Order=18, GroupName = "2. ReEntry")]
		public bool ContUseEndTime
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Time to disable continuation trades", Order=19, GroupName = "2. ReEntry")]
		public DateTime ContEndTime
		{ get; set; }


		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Stop Loss", Description = "Initial stop loss in ticks", Order=0, GroupName = "3. Exits")]
		public int StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "1st Take Profit", Description = "Take profit #1 in ticks", Order=3, GroupName = "3. Exits")]
		public int TakeProfit1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Exit Quantity #1", Description = "Number of contracts to close on TP1", Order=6, GroupName = "3. Exits")]
		public int QuantityTP1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "2nd Take Profit", Description = "Take profit #2 in ticks", Order=9, GroupName = "3. Exits")]
		public int TakeProfit2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Exit Quantity #2", Description = "Number of contracts to close on TP2", Order=12, GroupName = "3. Exits")]
		public int QuantityTP2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Final Take Profit", Description = "Final take profit in ticks. If using only one TP, use this one & zero the others", Order=15, GroupName = "3. Exits")]
		public int TakeProfit3
		{ get; set; }

		
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Daily Target Min $", Description = "Profit amount at which to stop trading for the day", Order=18, GroupName = "4. Targets")]
		public int DailyMinTarget
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Daily Loss Max $", Description = "Loss amount at which to stop trading for the day", Order=21, GroupName = "4. Targets")]
		public int DailyMaxLoss
		{ get; set; }
		
		
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #1", Description = "1st profit triger in ticks", Order=0, GroupName = "5. Break Even")]
		public int BETrigger1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #1", Description = "1st move to breakeven offset", Order=3, GroupName = "5. Break Even")]
		public int BEOffset1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #2", Description = "2nd profit triger in ticks", Order=6, GroupName = "5. Break Even")]
		public int BETrigger2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #2", Description = "2nd move to breakeven offset", Order=9, GroupName = "5. Break Even")]
		public int BEOffset2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #3", Description = "3rd profit triger in ticks", Order=12, GroupName = "5. Break Even")]
		public int BETrigger3
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #3", Description = "3rd move to breakeven offset", Order=15, GroupName = "5. Break Even")]
		public int BEOffset3
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #4", Description = "4th profit triger in ticks", Order=18, GroupName = "5. Break Even")]
		public int BETrigger4
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #4", Description = "4th move to breakeven offset", Order=21, GroupName = "5. Break Even")]
		public int BEOffset4
		{ get; set; }

		
		
		[NinjaScriptProperty]
		[Display(Name = "Use Trailing Stop", Description = "En/Disable use of Trailing Stop", Order=0, GroupName = "6. Trailing Stop")]
		public bool UseTrailingStop
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Use Simple Trail", Description = "Trail tick-for-tick", Order=3, GroupName = "6. Trailing Stop")]
		public bool TS_UseSimpleTrail
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Simple Trail Ticks", Description = "Number of ticks to use for a simple 1-for-1 trail", Order=6, GroupName = "6. Trailing Stop")]
		public int TS_SimpleTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Use Candle Close", Description = "Use candle close price, not H/L", Order=9, GroupName = "6. Trailing Stop")]
		public bool TS_UseClose
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Take Profit Ticks", Description = "Expand Take Profit to this when activated", Order=12, GroupName = "6. Trailing Stop")]
		public int TS_TakeProfitTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Tick Offset", Description = "Number of ticks offset from Swing or Candle H/L; Negative to allow more cushion", Order=15, GroupName = "6. Trailing Stop")]
		public int TS_OffsetTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Activation Ticks", Description = "Number of ticks in profit to activate trail", Order=18, GroupName = "6. Trailing Stop")]
		public int TS_ActivationTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Minimum Tick Distance", Description = "Minimum number of ticks SL can be placed from current price", Order=21, GroupName = "6. Trailing Stop")]
		public int TS_MinTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Maximum Tick Distance", Description = "Maximum number of ticks SL can be placed from current price", Order=24, GroupName = "6. Trailing Stop")]
		public int TS_MaxTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Adjust Min/Max Ticks #1", Description = "Adjust min/max number of ticks after reaching this profit level (Zero to disable)", Order=27, GroupName = "6. Trailing Stop")]
		public int TS_ReAdjustAtTicks1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Minimum Tick Distance #1", Description = "Minimum number of ticks SL can be placed from current price", Order=30, GroupName = "6. Trailing Stop")]
		public int TS_MinTicks1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Maximum Tick Distance #1", Description = "Maximum number of ticks SL can be placed from current price", Order=33, GroupName = "6. Trailing Stop")]
		public int TS_MaxTicks1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Adjust Min/Max Ticks #2", Description = "Adjust min/max number of ticks after reaching this profit level (Zero to disable)", Order=36, GroupName = "6. Trailing Stop")]
		public int TS_ReAdjustAtTicks2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Minimum Tick Distance #2", Description = "Minimum number of ticks SL can be placed from current price", Order=39, GroupName = "6. Trailing Stop")]
		public int TS_MinTicks2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Maximum Tick Distance #2", Description = "Maximum number of ticks SL can be placed from current price", Order=42, GroupName = "6. Trailing Stop")]
		public int TS_MaxTicks2
		{ get; set; }
		
		
		[NinjaScriptProperty]
		[Display(Name = "Use End of Day Trail", Description = "En/Disable Use of Trailing Stop at End of Day", Order=45, GroupName = "6. Trailing Stop")]
		public bool UseEoDTrail
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Trail Start Time", Description = "Enter Start Time for Trailing Stop", Order=48, GroupName = "6. Trailing Stop")]
		public DateTime TS_EoDStartTime
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "EoD Trail Ticks", Description = "Number of ticks offset from Swing or Candle H/L; Negative to allow more cushion", Order=51, GroupName = "6. Trailing Stop")]
		public int TS_EoDTicks
		{ get; set; }
		
		
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Average Volume Period", Description = "Period parameter to use for Average Volume", Order=0, GroupName = "7. Volume")]
		public int AveVolumePeriod
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Minimum Average Volume", Description = "Minimum Average Volume to place trade", Order=1, GroupName = "7. Volume")]
		public int MinAveVolume
		{ get; set; }
		
		
		
		[NinjaScriptProperty]
		[Display(Name = "Limit Trading Hours", Description = "Use Trading Session #1", Order=0, GroupName = "8. Trading Hours")]
		public bool LimitTradingHours
		{ get; set; }
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Entries", Order=3, GroupName = "8. Trading Hours")]
		public DateTime StartTime
		{ get; set; }
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Entries", Order=6, GroupName = "8. Trading Hours")]
		public DateTime EndTime
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all trades at given time", Order=9, GroupName = "8. Trading Hours")]
		public bool UseCloseTime
		{ get; set; }
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close All Trades", Order=12, GroupName = "8. Trading Hours")]
		public DateTime CloseTime
		{ get; set; }
		
		
		
		[NinjaScriptProperty]
		[Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=0, GroupName = "9. Misc / Debug")]
		public bool DisplayOCD
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Plot Historical", Description = "Plot historical trades from DB", Order=3, GroupName = "9. Misc / Debug")]
		public bool PlotHist
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Strategy Name", Description = "Name of Strategy", Order=6, GroupName = "9. Misc / Debug")]
		public string StrategyName
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=9, GroupName = "9. Misc / Debug")]
		public bool DisableLogging
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=12, GroupName = "9. Misc / Debug")]
		public bool UseOutput2
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Unique ID (v" + VERSION + ")", Description = "Unique number to identify strategy in Output window", Order=15, GroupName = "9. Misc / Debug")]
		public string UniqueID
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Use Market Entries", Description = "Enter using market order, not limit order", Order=18, GroupName = "9. Misc / Debug")]
		public bool UseMarket
		{ get; set; }
		#endregion
	}
}
